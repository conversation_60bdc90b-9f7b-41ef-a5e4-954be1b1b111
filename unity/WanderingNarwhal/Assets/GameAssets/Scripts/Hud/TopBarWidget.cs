using System;
using BBB;
using BBB.DI;
using BBB.Modals;
using Bebopbee.Core.Extensions.Unity;
using GameAssets.Scripts.Map.UI.Controllers;
using UnityEngine;

public abstract class TopBarWidget : SafeAnimatedWithInputUIComponent
{
    private const int TopSortingOrder = 150;

    private static readonly int Click = Animator.StringToHash("Click");

    [SerializeField] private Canvas _canvas;
    [SerializeField] private bool _keepInteractableWhenTemporaryShown;
    [SerializeField] private GameObject[] _clickabilityMarkers;

    private bool _shouldBeHiddenAfterTemporaryShowing = true;
    private bool _shownByAnimatedDelta;

    protected IModalsManager ModalsManager { get; private set; }
    protected IScreensManager ScreensManager { get; private set; }
    protected Action TemporaryCallback { get; private set; }
    protected bool TemporaryShown { get; private set; }

    public virtual void Init(IContext context)
    {
        ScreensManager = context.Resolve<IScreensManager>();
        ModalsManager = context.Resolve<IModalsManager>();

        _graphicRaycaster.enabled = IsShown;
        _canvas.overrideSorting = false;
    }

    public override void Show()
    {
        base.Show();
        _shouldBeHiddenAfterTemporaryShowing = false;
    }

    public override void Hide()
    {
        if (!TemporaryShown)
            base.Hide();

        _shouldBeHiddenAfterTemporaryShowing = true;
    }

    public void SetTemporaryCallback(Action temporaryCallback)
    {
        TemporaryCallback = temporaryCallback;
        RefreshClickabilityMarkers();
    }

    public void TemporaryShow(bool animatedDelta = false)
    {
        _shownByAnimatedDelta = animatedDelta;
        TemporaryShown = true;
        RefreshClickabilityMarkers();

        if (!IsShown)
        {
            if (_animator != null)
            {
                ResetAnimator();
                PlayShowAnimation();
            }

            IsShown = true;

            OnTemporaryShow();
        }
        // if widget is shown under the modal (we do not hide the hud all the time now), we want to still call for temporary show as we need layer change
        // common scenarios are various source of rewarding on social screen
        else if (ModalsManager.IsShowingAModal())
        {
            OnTemporaryShow();
        }
    }

    public void StopTemporaryShow(bool animatedDelta = false)
    {
        if (_shownByAnimatedDelta && !animatedDelta)
            return;

        TemporaryShown = false;
        _shownByAnimatedDelta = false;
        TemporaryCallback = null;

        RefreshClickabilityMarkers();

        if (_shouldBeHiddenAfterTemporaryShowing)
        {
            Hide();
        }

        OnTemporaryHide();
    }

    protected virtual void OnTemporaryShow()
    {
        MoveCanvasOnTop();
    }

    protected virtual void OnTemporaryHide()
    {
        MoveCanvasBack();
    }

    public abstract void ShowBalance();
    public abstract void HideBalance();

    private void RefreshClickabilityMarkers()
    {
        var shouldBeEnabled = !TemporaryShown || (TemporaryShown && TemporaryCallback != null);
        _clickabilityMarkers.Enable(shouldBeEnabled);
    }

    private void MoveCanvasOnTop()
    {
        _canvas.overrideSorting = true;
        _canvas.sortingOrder = TopSortingOrder;
        _graphicRaycaster.enabled = _keepInteractableWhenTemporaryShown;
    }

    private void MoveCanvasBack()
    {
        _canvas.overrideSorting = false;
        _graphicRaycaster.enabled = true;
    }

    protected void TriggerClickAction(InterruptionData interruptionData)
    {
        _animator.SetTrigger(Click);
        if (ScreensManager.GetCurrentController() is IFlowScreenController flowScreenController)
        {
            flowScreenController.InterruptFlow(interruptionData);
        }
        else
        {
            interruptionData.Action.Invoke();
        }
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        TemporaryCallback = null;
    }
}