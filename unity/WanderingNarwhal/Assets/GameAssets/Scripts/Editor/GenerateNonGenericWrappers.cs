using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using FBConfig;

namespace Core.Configs.Editor
{
    public class WrapperInfo
    {
        public string WrapperName => $"{ConfigType.Name}Wrapper";
        public Type DictType { get; set; }
        public Type ConfigType { get; set; }
        public Type ConfigTType { get; set; }
        public string PropertyName { get; set; }

        public WrapperInfo(Type dictType, Type configType, Type configTType, string propertyName = null)
        {
            DictType = dictType;
            ConfigType = configType;
            ConfigTType = configTType;
            PropertyName = propertyName ?? ConfigType.Name;
        }
    }

    public static class NonGenericWrapperGenerator
    {
        [MenuItem("BebopBee/Configs/Generate Non-Generic Wrappers")]
        public static void GenerateNonGenericWrappers()
        {
            try
            {
                var outputDirectory = Path.Combine(Application.dataPath, "GameAssets/Flatbuffers/Wrappers");

                UnityEngine.Debug.Log($"Getting wrapper information...");

                var wrappers = GetWrapperInfoList();

                if (wrappers.Count == 0)
                {
                    UnityEngine.Debug.LogError("No wrapper classes found!");
                    return;
                }

                UnityEngine.Debug.Log($"Found {wrappers.Count} wrapper classes:");
                foreach (var wrapper in wrappers)
                {
                    UnityEngine.Debug.Log($"  - {wrapper.WrapperName}");
                }

                UnityEngine.Debug.Log($"Generating non-generic wrappers to {outputDirectory}...");

                // Create output directory if it doesn't exist
                if (!Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                    UnityEngine.Debug.Log($"Created directory: {outputDirectory}");
                }

                // Clean up old generated files first
                CleanupOldGeneratedFiles(outputDirectory);

                // Generate each wrapper in its own file
                var generatedFiles = new List<string>();
                foreach (var wrapper in wrappers)
                {
                    var fileName = $"{wrapper.WrapperName}_generated.cs";
                    var filePath = Path.Combine(outputDirectory, fileName);
                    GenerateWrapperFile(wrapper, filePath);
                    generatedFiles.Add(fileName);
                }

                UnityEngine.Debug.Log($"✅ Successfully generated {wrappers.Count} non-generic wrapper classes!");
                UnityEngine.Debug.Log($"Output directory: {outputDirectory}");
                UnityEngine.Debug.Log($"Generated files:");
                foreach (var file in generatedFiles)
                {
                    UnityEngine.Debug.Log($"  - {file}");
                }

                // Refresh the asset database to show the new files in Unity
                AssetDatabase.Refresh();

                UnityEngine.Debug.Log("📋 Next steps:");
                UnityEngine.Debug.Log("1. Review the generated wrapper files in Assets/GameAssets/Flatbuffers/Wrappers/");
                UnityEngine.Debug.Log("2. Update FlatBufferGroupedConfigResolver.cs to use the new non-generic wrappers");
                UnityEngine.Debug.Log("3. Remove the old generic wrapper classes from FlatBufferGroupedConfigResolver.cs");
                UnityEngine.Debug.Log("4. Test the changes to ensure everything works correctly");

                // Show the output directory in the project window
                var directoryAsset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>("Assets/GameAssets/Flatbuffers/Wrappers");
                if (directoryAsset != null)
                {
                    EditorGUIUtility.PingObject(directoryAsset);
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error generating non-generic wrappers: {ex.Message}\n{ex.StackTrace}");
            }
        }

        private static List<WrapperInfo> GetWrapperInfoList() => new List<WrapperInfo>
            {
                new(typeof(AudioMixerConfigDict), typeof(AudioMixerConfig), typeof(AudioMixerConfigT)),
                new(typeof(CountriesTiersConfigDict), typeof(CountriesTiersConfig), typeof(CountriesTiersConfigT)),
                new(typeof(IAPBasketConfigDict), typeof(IAPBasketConfig), typeof(IAPBasketConfigT), "IapBasketConfig"),
                new(typeof(NarrativeDialogConfigDict), typeof(NarrativeDialogConfig), typeof(NarrativeDialogConfigT)),
                new(typeof(DailyTriviaConfigDict), typeof(DailyTriviaConfig), typeof(DailyTriviaConfigT)),
                new(typeof(LevelAdsConfigDict), typeof(LevelAdsConfig), typeof(LevelAdsConfigT)),
                new(typeof(GameEventConfigDict), typeof(GameEventConfig), typeof(GameEventConfigT)),
                new(typeof(RaceGameEventConfigDict), typeof(RaceGameEventConfig), typeof(RaceGameEventConfigT)),
                new(typeof(RoyaleGameEventConfigDict), typeof(RoyaleGameEventConfig), typeof(RoyaleGameEventConfigT)),
                new(typeof(GameEventMetaConfigDict), typeof(GameEventMetaConfig), typeof(GameEventMetaConfigT)),
                new(typeof(IAPStoreCategoryConfigDict), typeof(IAPStoreCategoryConfig), typeof(IAPStoreCategoryConfigT), "IapStoreCategoryConfig"),
                new(typeof(IAPStoreVirtualItemPackConfigDict), typeof(IAPStoreVirtualItemPackConfig), typeof(IAPStoreVirtualItemPackConfigT), "IapStoreVirtualItemPackConfig"),
                new(typeof(OfferConfigDict), typeof(OfferConfig), typeof(OfferConfigT)),
                new(typeof(BoosterConfigDict), typeof(BoosterConfig), typeof(BoosterConfigT)),
                new(typeof(GachaConfigDict), typeof(GachaConfig), typeof(GachaConfigT)),
                new(typeof(Match3SpecialVoiceoversConfigDict), typeof(Match3SpecialVoiceoversConfig), typeof(Match3SpecialVoiceoversConfigT)),
                new(typeof(LivesConfigDict), typeof(LivesConfig), typeof(LivesConfigT)),
                new(typeof(HintSystemConfigDict), typeof(HintSystemConfig), typeof(HintSystemConfigT)),
                new(typeof(SuperBoostProgressConfigDict), typeof(SuperBoostProgressConfig), typeof(SuperBoostProgressConfigT)),
                new(typeof(SuperBoostConfigDict), typeof(SuperBoostConfig), typeof(SuperBoostConfigT)),
                new(typeof(ScreenConfigDict), typeof(ScreenConfig), typeof(ScreenConfigT)),
                new(typeof(FakeUsersConfigDict), typeof(FakeUsersConfig), typeof(FakeUsersConfigT)),
                new(typeof(LocalNotificationsConfigDict), typeof(LocalNotificationsConfig), typeof(LocalNotificationsConfigT)),
                new(typeof(LocalPushNotificationsTimingConfigDict), typeof(LocalPushNotificationsTimingConfig), typeof(LocalPushNotificationsTimingConfigT)),
                new(typeof(SystemConfigDict), typeof(SystemConfig), typeof(SystemConfigT)),
                new(typeof(CompetitionGameEventConfigDict), typeof(CompetitionGameEventConfig), typeof(CompetitionGameEventConfigT)),
                new(typeof(ChallengeTriviaRewardsConfigDict), typeof(ChallengeTriviaRewardsConfig), typeof(ChallengeTriviaRewardsConfigT)),
                new(typeof(ChallengeConfigDict), typeof(ChallengeConfig), typeof(ChallengeConfigT)),
                new(typeof(UnifiedPromotionConfigDict), typeof(UnifiedPromotionConfig), typeof(UnifiedPromotionConfigT)),
                new(typeof(ThemeConfigDict), typeof(ThemeConfig), typeof(ThemeConfigT)),
                new(typeof(ThemeMetaConfigDict), typeof(ThemeMetaConfig), typeof(ThemeMetaConfigT)),
                new(typeof(DefaultNamesConfigDict), typeof(DefaultNamesConfig), typeof(DefaultNamesConfigT)),
                new(typeof(EndlessTreasureConfigDict), typeof(EndlessTreasureConfig), typeof(EndlessTreasureConfigT)),
                new(typeof(RaceStageConfigDict), typeof(RaceStageConfig), typeof(RaceStageConfigT)),
                new(typeof(TeamEventConfigDict), typeof(TeamEventConfig), typeof(TeamEventConfigT)),
                new(typeof(PlayerSkillConfigDict), typeof(PlayerSkillConfig), typeof(PlayerSkillConfigT)),
                new(typeof(WeeklyLeaderboardConfigDict), typeof(WeeklyLeaderboardConfig), typeof(WeeklyLeaderboardConfigT)),
                new(typeof(AdPlacementConfigDict), typeof(AdPlacementConfig), typeof(AdPlacementConfigT)),
                new(typeof(ButlerGiftConfigDict), typeof(ButlerGiftConfig), typeof(ButlerGiftConfigT)),
                new(typeof(AssistSystemConfigDict), typeof(AssistSystemConfig), typeof(AssistSystemConfigT)),
                new(typeof(SceneTaskConfigDict), typeof(SceneTaskConfig), typeof(SceneTaskConfigT)),
                new(typeof(DailyTaskSettingsConfigDict), typeof(DailyTaskSettingsConfig), typeof(DailyTaskSettingsConfigT)),
                new(typeof(CarrotsConfigDict), typeof(CarrotsConfig), typeof(CarrotsConfigT)),
                new(typeof(SdbConfigDict), typeof(SdbConfig), typeof(SdbConfigT)),
                new(typeof(SweepStakesGameEventConfigDict), typeof(SweepStakesGameEventConfig), typeof(SweepStakesGameEventConfigT)),
                new(typeof(SweepstakesVideoConfigDict), typeof(SweepstakesVideoConfig), typeof(SweepstakesVideoConfigT)),
                new(typeof(IAPStoreMarketItemConfigDict), typeof(IAPStoreMarketItemConfig), typeof(IAPStoreMarketItemConfigT), "IapStoreMarketItemConfig"),
                new(typeof(QuestConfigDict), typeof(QuestConfig), typeof(QuestConfigT)),
                new(typeof(SlotMachineOutcomeConfigDict), typeof(SlotMachineOutcomeConfig), typeof(SlotMachineOutcomeConfigT)),
                new(typeof(MechanicTargetingConfigDict), typeof(MechanicTargetingConfig), typeof(MechanicTargetingConfigT)),
                new(typeof(GiantPinataOutcomeConfigDict), typeof(GiantPinataOutcomeConfig), typeof(GiantPinataOutcomeConfigT)),
                new(typeof(LockItemConfigDict), typeof(LockItemConfig), typeof(LockItemConfigT)),
                new(typeof(GameUpdateConfigDict), typeof(GameUpdateConfig), typeof(GameUpdateConfigT)),
                new(typeof(SocialConfigDict), typeof(SocialConfig), typeof(SocialConfigT)),
                new(typeof(LevelNarrativeConfigDict), typeof(LevelNarrativeConfig), typeof(LevelNarrativeConfigT)),
                new(typeof(IceBreakerConfigDict), typeof(IceBreakerConfig), typeof(IceBreakerConfigT)),
                new(typeof(AutoPopupPriorityConfigDict), typeof(AutoPopupPriorityConfig), typeof(AutoPopupPriorityConfigT)),
                new(typeof(HudAssetReferenceConfigDict), typeof(HudAssetReferenceConfig), typeof(HudAssetReferenceConfigT)),
                new(typeof(HudConfigDict), typeof(HudConfig), typeof(HudConfigT)),
                new(typeof(CollectionConfigDict), typeof(CollectionConfig), typeof(CollectionConfigT)),
                new(typeof(TutorialConfigDict), typeof(TutorialConfig), typeof(TutorialConfigT)),
                new(typeof(DailyTasksConfigDict), typeof(DailyTasksConfig), typeof(DailyTasksConfigT)),
                new(typeof(ChallengeTriviaConfigDict), typeof(ChallengeTriviaConfig), typeof(ChallengeTriviaConfigT)),
                new(typeof(CollectionCardsConfigDict), typeof(CollectionCardsConfig), typeof(CollectionCardsConfigT)),
                new(typeof(ScenesConfigDict), typeof(ScenesConfig), typeof(ScenesConfigT)),
                new(typeof(ChallengeLocationConfigDict), typeof(ChallengeLocationConfig), typeof(ChallengeLocationConfigT)),
                new(typeof(SweepstakesDailyLoginConfigDict), typeof(SweepstakesDailyLoginConfig), typeof(SweepstakesDailyLoginConfigT)),
                new(typeof(QuickActionsConfigDict), typeof(QuickActionsConfig), typeof(QuickActionsConfigT))
            };

        private static int CleanupOldGeneratedFiles(string outputDirectory)
        {
            if (!Directory.Exists(outputDirectory))
                return 0;

            var deletedCount = 0;
            var files = Directory.GetFiles(outputDirectory, "*_generated.cs", SearchOption.TopDirectoryOnly);
            
            foreach (var file in files)
            {
                try
                {
                    File.Delete(file);
                    deletedCount++;
                    UnityEngine.Debug.Log($"Deleted old generated file: {Path.GetFileName(file)}");
                }
                catch (Exception ex)
                {
                    UnityEngine.Debug.LogWarning($"Failed to delete {file}: {ex.Message}");
                }
            }

            return deletedCount;
        }

        private static void GenerateWrapperFile(WrapperInfo wrapperInfo, string outputFilePath)
        {
            var sb = new StringBuilder();

            // Generate file header
            sb.AppendLine("using System;");
            sb.AppendLine("using System.Collections;");
            sb.AppendLine("using System.Collections.Generic;");
            sb.AppendLine("using BBB;");
            sb.AppendLine("using BBB.Core;");
            sb.AppendLine("using FBConfig;");
            sb.AppendLine("using UnityEngine.Profiling;");
            sb.AppendLine();
            sb.AppendLine("namespace Core.Configs");
            sb.AppendLine("{");

            // Generate the wrapper
            sb.AppendLine(GenerateNonGenericWrapper(wrapperInfo));

            // Generate file footer
            sb.AppendLine("}");

            File.WriteAllText(outputFilePath, sb.ToString());
        }

        private static string GenerateNonGenericWrapper(WrapperInfo wrapperInfo)
        {
            return $@"    internal class {wrapperInfo.WrapperName} : IDictionary<string, {wrapperInfo.ConfigType.Name}>, IFlatbufferWrapper
    {{
        private readonly FlatbufferConfig _collectionProvider;

        private {wrapperInfo.DictType.Name}? _collection;
        private {wrapperInfo.DictType.Name}? Collection
        {{
            get
            {{
                if (!_collection.HasValue)
                {{
                    _collection = _collectionProvider.{wrapperInfo.PropertyName};
                }}
                return _collection;
            }}
        }}

        private int CollectionLength => Collection?.ValuesLength ?? 0;
        private Dictionary<string, {wrapperInfo.ConfigType.Name}> _internalDict;
        private Dictionary<string, {wrapperInfo.ConfigType.Name}> InternalDict => _internalDict ??= new();
        private Dictionary<int, string> _indexKeyMap;
        private Dictionary<int, string> IndexKeyMap => _indexKeyMap ??= new();
        public string Hash => Collection?.Hash ?? string.Empty;

        public {wrapperInfo.WrapperName}({wrapperInfo.DictType.Name}? collection)
        {{
            _collection = collection;
        }}

        public {wrapperInfo.WrapperName}(FlatbufferConfig collectionProvider)
        {{
            _collectionProvider = collectionProvider;
        }}

        public IEnumerator<KeyValuePair<string, {wrapperInfo.ConfigType.Name}>> GetEnumerator()
        {{
            if (!Collection.HasValue)
                yield break;

            for (int i = 0; i < CollectionLength; i++)
            {{
                var item = this[i];
                yield return new KeyValuePair<string, {wrapperInfo.ConfigType.Name}>(item.Uid, item);
            }}
        }}

        IEnumerator IEnumerable.GetEnumerator()
        {{
            return GetEnumerator();
        }}

        public void Add(KeyValuePair<string, {wrapperInfo.ConfigType.Name}> item)
        {{
            throw new NotImplementedException();
        }}

        public void Clear()
        {{
            throw new NotImplementedException();
        }}

        public bool Contains(KeyValuePair<string, {wrapperInfo.ConfigType.Name}> item)
        {{
            return TryGetValue(item.Key, out var itemValue) && itemValue.Equals(item.Value);
        }}

        public void CopyTo(KeyValuePair<string, {wrapperInfo.ConfigType.Name}>[] array, int arrayIndex)
        {{
            throw new NotImplementedException();
        }}

        public bool Remove(KeyValuePair<string, {wrapperInfo.ConfigType.Name}> item)
        {{
            throw new NotImplementedException();
        }}

        public int Count => CollectionLength;

        public bool IsReadOnly => true;

        public void Add(string key, {wrapperInfo.ConfigType.Name} value)
        {{
            throw new NotImplementedException();
        }}

        public bool ContainsKey(string key)
        {{
            return TryGetValue(key, out _);
        }}

        private bool TryLookupByKey(string key, out {wrapperInfo.ConfigType.Name} value)
        {{
            if (InternalDict.TryGetValue(key, out value))
                return true;

            Profiler.BeginSample(""{wrapperInfo.WrapperName}.TryLookupByKey"");
            value = default;
            if (!Collection.HasValue)
            {{
                Profiler.EndSample();
                return false;
            }}

            var min = 0;
            var max = CollectionLength - 1;
            while (min <= max)
            {{
                var mid = (min + max) / 2;
                value = this[mid];
                InternalDict[value.Uid] = value;
                var comp = string.CompareOrdinal(value.Uid, key);
                if (comp == 0)
                {{
                    Profiler.EndSample();
                    return true;
                }}

                if (comp > 0)
                {{
                    max = mid - 1;
                }}
                else
                {{
                    min = mid + 1;
                }}
            }}
            Profiler.EndSample();
            value = default;
            return false;
        }}

        public bool Remove(string key)
        {{
            throw new NotImplementedException();
        }}

        public bool TryGetValue(string key, out {wrapperInfo.ConfigType.Name} value)
        {{
            Profiler.BeginSample(""{wrapperInfo.WrapperName}.TryGetValue"");
            var result = TryLookupByKey(key, out value);
            Profiler.EndSample();
            return result;
        }}

        public {wrapperInfo.ConfigType.Name} this[string key]
        {{
            get
            {{
                TryGetValue(key, out var item);
                return item;
            }}
            set => InternalDict[key] = value;
        }}

        private {wrapperInfo.ConfigType.Name} this[int index]
        {{
            get
            {{
                if (IndexKeyMap.TryGetValue(index, out var key) && InternalDict.TryGetValue(key, out var item))
                {{
                    return item;
                }}

                var newValue = Collection?.Values(index);
                if (newValue.HasValue)
                {{
                    var uid = newValue.Value.Uid;
                    IndexKeyMap[index] = uid;
                    if (InternalDict.TryGetValue(uid, out var existingItem))
                        return existingItem;

                    InternalDict[uid] = newValue.Value;
                }}
                return newValue ?? default;
            }}
        }}

        public ICollection<string> Keys => throw new NotImplementedException();
        public ICollection<{wrapperInfo.ConfigType.Name}> Values
        {{
            get
            {{
#if UNITY_EDITOR
                // for editor usage only (like tests and level editor), {wrapperInfo.WrapperName} shouldn't be used to iterate over all values
                BDebug.LogError(LogCat.Config, ""{wrapperInfo.WrapperName}.Values is implemented for editor usage only. This will throw an exception on device. Use FlatBufferConfigResolver instead."");
                var result = new {wrapperInfo.ConfigType.Name}[CollectionLength];
                for (int i = 0; i < CollectionLength; i++)
                    result[i] = this[i];
                return result;
#else
                throw new NotImplementedException();
#endif
            }}
        }}
    }}";
        }
    }
}
