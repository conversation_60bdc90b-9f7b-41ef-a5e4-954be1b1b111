using System;
using System.Collections.Generic;
using BBB;
using BBB.CellTypes;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Logic.Tiles;

public class TileSpawnHandler
{
    public struct DelayedSpawnRecord
    {
        public Coords From;
        public Tile Tile;
        public TileAsset TileAsset;
        public int Delay;
        public int SourceTileId;
        public GoalType GoalType;
    }

    private const int MaxOverdueDelay = 50;
    private const int SpawnedTileBusyTime = 2;

    private static readonly Func<Cell, bool> IsCellWithRegularTileOrEmptyFunc = IsCellWithRegularTileOrEmpty;

    public static readonly List<DelayedSpawnRecord> DelayedSpawnReactions = new(20);

    public static TileState SpawnedTilesOnCurrentStep = TileState.None;

    public static void HandleSpawnTileAtRandomPosition(Grid grid, GoalsSystem goalSystem,
        TileHitReactionHandler reactionHandler, Coords from, int sourceTileId, TileState sourceState, Tile tile)
    {
        if ((sourceState & TileState.HiveMod) != 0)
        {
            if (HiveTile.CanHitBeehive(from, grid, goalSystem, reactionHandler) <= 0)
            {
                return;
            }
        }

        // All spawn reactions must be delayed, since target cell may take some steps to be available
        // Actual add new tile to the grid is happening in TryDoSpawnTileAtPosition.
        SpawnedTilesOnCurrentStep |= sourceState;

        var count = tile.GetParam(TileParamEnum.TileCreateCountForReaction);
        for (var i = 0; i < count; i++)
        {
            // At this stage we don't have exact target cell for spawning, so we assign what default cell[0].
            var originCell = grid.Cells[0];
            var (newTile, tileAsset, goalType) = tile.CreateTileFromReaction(grid.TilesSpawnedCount++, originCell, i);

            if (tileAsset == TileAsset.Undefined)
            {
                continue;
            }
            //Adding delay 1 for tiles with ShouldDelaySimulationOnReaction(SlotMachine and Fireworks) true to prevent targeting falling tiles 
            var delay = tile.ShouldDelaySimulationOnReaction ? 1 : 0;
            DelayedSpawnReactions.Add(new DelayedSpawnRecord
            {
                From = from,
                SourceTileId = sourceTileId,
                Tile = newTile,
                Delay = delay,
                TileAsset = tileAsset,
                GoalType = goalType
            });
        }
    }

    public static void HandleDelaySpawnedReactions(Grid grid, IRootSimulationHandler events,
        TileHitReactionHandler reactionHandler,
        SimulationInputParams inputParams, GoalsSystem goalSystem, PopSystem popSystem)
    {
        if (DelayedSpawnReactions.Count == 0) return;

        var potentialCellList = grid.GetPrioritizedMatchingCells(IsCellWithRegularTileOrEmptyFunc);

        for (var i = DelayedSpawnReactions.Count - 1; i >= 0; i--)
        {
            var item = DelayedSpawnReactions[i];
            var delay = item.Delay - 1;

            if (delay <= 0)
            {
                if (potentialCellList.Count > 0)
                {
                    var potentialCell = potentialCellList[^1];
                    potentialCellList.RemoveAt(potentialCellList.Count - 1);

                    if (TryDoSpawnTileAtPosition(grid, events, reactionHandler, inputParams, item.Tile,
                            item.SourceTileId, item.From, item.TileAsset, item.GoalType, goalSystem, potentialCell))
                    {
                        DelayedSpawnReactions.RemoveAt(i);
                        continue;
                    }
                }

                if (delay > -MaxOverdueDelay)
                {
                    item.Delay = delay;
                    DelayedSpawnReactions[i] = item;
                }
                else
                {
                    DelayedSpawnReactions.RemoveAt(i);
                    GoalCollectHandler.HandleCollectGoal(grid, goalSystem, popSystem, reactionHandler, inputParams,
                        events, null, item.From, item.Tile.Id, item.Tile.Speciality,
                        item.Tile.State, item.Tile.Kind, null);
                }
            }
            else
            {
                item.Delay = delay;
                DelayedSpawnReactions[i] = item;
            }
        }
    }

    private static bool TryDoSpawnTileAtPosition(Grid grid, IRootSimulationHandler events,
        TileHitReactionHandler reactionHandler,
        SimulationInputParams inputParams, Tile tile, int sourceTileId, Coords from,
        TileAsset tileAsset, GoalType goalType, GoalsSystem goalsSystem, Cell randomTileCell)
    {
        if (randomTileCell == null ||
            !randomTileCell.Tile.IsNull() && (randomTileCell.Tile.State & TileState.InTransition) != 0)
            return false;

        var spawnTileAction =
            new ActionSpawnTileFromAnotherTile(sourceTileId, from, randomTileCell.Coords, tile, tileAsset);
        events.AddAction(spawnTileAction);

        if (randomTileCell.Tile != null)
        {
            if (randomTileCell.Tile.Kind == TileKinds.Undefined)
            {
                events.OnTileKindDefined(randomTileCell.Tile.Id, randomTileCell.Tile.Kind);
            }

            reactionHandler.RegisterTileShouldReactOnDie(grid, events, null, randomTileCell.Coords,
                randomTileCell.Tile.Id, randomTileCell.Tile.Asset, randomTileCell.Tile.Speciality,
                randomTileCell.Tile.State, randomTileCell.Tile.Kind, randomTileCell.Tile);

            randomTileCell.ReplaceTile(tile);
            events.AddAction(new ActionReplace(randomTileCell.Coords, tile, goalType));
        }
        else
        {
            randomTileCell.AddTile(tile);
            events.AddAction(new ActionSpawn(randomTileCell.Coords, tile, null, goalType));
        }

        if (goalType != GoalType.None)
        {
            goalsSystem.TryAddGoalIfNeeded(goalType);
        }

        randomTileCell.IsBusy += SpawnedTileBusyTime;

        reactionHandler.NewBusyCells ??= new List<Cell>(3);
        reactionHandler.NewBusyCells.Add(randomTileCell);

        return true;
    }

    private static bool IsCellWithRegularTileOrEmpty(Cell cell)
    {
        if (cell == null || cell.IsAnyOf(CellState.Ivy | CellState.Water | CellState.FlagEnd) ||
            cell.HasMultiSizeCellReference())
        {
            return false;
        }

        if (cell.Tile.IsNull())
        {
            return cell.CanAcceptTile();
        }

        return cell.Tile.Asset == TileAsset.Simple && cell.Tile.State == TileState.None;
    }
}