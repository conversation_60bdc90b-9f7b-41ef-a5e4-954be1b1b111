using BBB.Audio;
using BBB.Match3.Renderer;
using BebopBee.Core.Audio;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionPropellerExplosion : Match3ActionBase
    {
        private readonly PropellerActiveBoardObjectPredicate _activeObjectPredicate;
        private readonly int _propellerId;
        private readonly Cell _targetCell;
        private readonly Coords _sourceCoords;

        public ActionPropellerExplosion(int propellerId, Cell cell, Coords sourceCoords)
        {
            _activeObjectPredicate = new PropellerActiveBoardObjectPredicate
            {
                PropellerId = propellerId
            };
            _propellerId = propellerId;
            _targetCell = cell;
            _sourceCoords = sourceCoords;
            AffectedCoords.Add(_sourceCoords);
            AffectedCoords.Add(_targetCell.Coords);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ReleaseObjects(proxy);
        }

        private void ReleaseObjects(PlaySimulationActionProxy proxy)
        {
            proxy.TileTickPlayer.DeleteObjects(obj => obj is PropellerTarget pt && pt.PropellerId == _propellerId);
            ReleasedCoords.Add(_targetCell.Coords);
            ReleasedCoords.Add(_sourceCoords);
        }

        protected override bool CanExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            return proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_activeObjectPredicate) && base.CanExecute(grid, proxy);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var settings = proxy.Settings.GetSettingsForEffect<PropellerEffectSettings>(FxType.PropellerFlight);

            var targetPos = _targetCell.Coords.ToUnityVector2();

            targetPos = Tile.GetCenterPointForIfSquareMechanic(grid, _targetCell, targetPos, settings.PropellerTargetCenterOfSquareMechanic);

            AudioProxy.PlaySound(Match3SoundIds.PropellerLand);
            proxy.FXRenderer.SpawnPropellerDestroy(targetPos);

            ReleaseObjects(proxy);
        }
    }
}