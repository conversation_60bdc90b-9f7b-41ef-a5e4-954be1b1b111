using BBB.Audio;
using BBB.Match3.Renderer;
using BebopBee.Core.Audio;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionFireWorksExplosion : Match3ActionBase
    {
        private readonly FireWorksActiveBoardObjectPredicate _activeObjectPredicate;
        private readonly int _fireWorksId;
        private readonly Cell _targetCell;
        private readonly Coords _sourceCoords;

        public ActionFireWorksExplosion(int fireWorKsId, Cell cell, Coords sourceCoords)
        {
            _activeObjectPredicate = new FireWorksActiveBoardObjectPredicate
            {
                FireWorksId = fireWorKsId
            };
            _fireWorksId = fireWorKsId;
            _targetCell = cell;
            _sourceCoords = sourceCoords;
            AffectedCoords.Add(_targetCell.Coords);
            AffectedCoords.Add(_sourceCoords);
        }

        protected override string GetMembersString()
        {
            return $"targetCoords={_targetCell?.Coords} uniqueId={_fireWorksId}";
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ReleaseObjects(proxy);
        }

        private void ReleaseObjects(PlaySimulationActionProxy proxy)
        {
            proxy.TileTickPlayer.DeleteObjects(obj => obj is FireWorksTarget pt && pt.FireWorksId == _fireWorksId);
            ReleasedCoords.Add(_targetCell.Coords);
            ReleasedCoords.Add(_sourceCoords);
        }

        protected override bool CanExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            return proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_activeObjectPredicate) && base.CanExecute(grid, proxy);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            AudioProxy.PlaySound(Match3SoundIds.FireworksRocketLand);

            var targetPos = _targetCell.Coords.ToUnityVector2();
            var settings = proxy.Settings.GetSettingsForEffect<FireWorksEffectSettings>(FxType.FireWorksFlight);
            proxy.TileTickPlayer.BoardObjectFactory.CreateStaticOccupier(settings.ReleaseFireWorksRocketTargetFx, targetPos);

            targetPos = Tile.GetCenterPointForIfSquareMechanic(grid, _targetCell, targetPos, settings.FireWorksTargetCenterOfSquareMechanic);

            proxy.FXRenderer.SpawnFireWorksDestroy(targetPos);

            ReleaseObjects(proxy);
        }
    }
}