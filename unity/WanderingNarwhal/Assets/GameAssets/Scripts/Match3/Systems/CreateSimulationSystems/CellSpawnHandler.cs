using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static class CellSpawnHandler
    {
        public static void HandleSpawnSquareTile(GoalsSystem goalsSystem, Grid grid, IRootSimulationHandler events,
            Coords from, TileSpeciality tileSpeciality, HitWaitParams hitWaitParams)
        {
            var cellsToSpawn = GetSpawnSquareData(grid, from, tileSpeciality,
                out var cellState, out var goalType, out var fxType);
            
            foreach (var variable in cellsToSpawn)
            {
                if (!grid.TryGetCell(variable.coords, out var cell))
                {
                    continue;
                }
                
                cell.BackgroundCount = 1;
                cell.Add(cellState);
                goalsSystem.TryAddGoalIfNeeded(goalType);
            }

            cellsToSpawn.Sort((cell1, cell2) => cell1.position.CompareTo(cell2.position));

            var sortedList = new List<Coords>(cellsToSpawn.Count);

            foreach (var cell in cellsToSpawn)
            {
                sortedList.Add(cell.coords);
            }

            events.AddAction(new ActionSpawnInCells(from, sortedList, cellState, goalType, fxType, hitWaitParams));
        }

        private static List<(Coords coords, int position)> GetSpawnSquareData(Grid grid, Coords fromCoords,
            TileSpeciality tileSpeciality, out CellState cellState, out GoalType goalType, out FxType fxType)
        {
            int[] positionList;
            var spawnSuitableList = new List<(Coords coords, int position)>();

            Coords min;
            Coords max;

            switch (tileSpeciality)
            {
                case TileSpeciality.FlowerPot:
                {
                    min = new Coords(fromCoords.X - 1, fromCoords.Y - 1);
                    max = new Coords(fromCoords.X + 1, fromCoords.Y + 1);

                    cellState = CellState.Petal;
                    goalType = GoalType.Petal;
                    fxType = FxType.PetalAnticipation;
                    positionList = M3Constants.FlowerPotSpreadPositions;
                    const int positionCount = 9;
                    if (positionList is not { Length: positionCount })
                    {
                        positionList = new int[positionCount];
                        for (var i = 0; i < positionCount; i++)
                        {
                            positionList[i] = i;
                        }
                    }

                    break;
                }
                case TileSpeciality.Bush:
                {
                    min = new Coords(fromCoords.X - 1, fromCoords.Y - 1);
                    max = new Coords(fromCoords.X + 2, fromCoords.Y + 2);

                    cellState = CellState.BackOne;
                    goalType = GoalType.Backgrounds;
                    fxType = FxType.GrassAnticipation;
                    positionList = M3Constants.BushSpreadPositions;

                    const int positionCount = 16;
                    if (positionList is not { Length: positionCount })
                    {
                        positionList = new int[positionCount];
                        for (var i = 0; i < positionCount; i++)
                        {
                            positionList[i] = i;
                        }
                    }

                    break;
                }
                default:
                {
                    cellState = CellState.None;
                    goalType = GoalType.None;
                    fxType = FxType.GrassAnticipation;
                    return spawnSuitableList; // Return an empty list early
                }
            }

            var positionIndex = 0;

            for (var x = min.X; x <= max.X; x++)
            for (var y = min.Y; y <= max.Y; y++)
            {
                var coords = new Coords(x, y);

                var index = positionList[positionIndex++];
                //checks if coords are within the grid
                if (!grid.TryGetCell(coords, out var cell))
                {
                    continue;
                }

                if (IsCellSuitableForSpawn(cell))
                {
                    spawnSuitableList.Add((coords, index));
                }
            }

            return spawnSuitableList;
        }

        private static bool IsCellSuitableForSpawn(Cell cell)
        {
            if (cell.IsAnyOf(CellState.BackOne | CellState.BackDouble | CellState.Petal | CellState.Water |
                             CellState.FlagEnd))
            {
                return false;
            }

            cell = cell.GetMainCellReference(out _);

            if (cell.Tile.IsNull())
            {
                return true;
            }

            return !cell.Tile.DisallowCellBackgroundSpawn();
        }
    }
}