using BBB.Audio;
using BBB.Match3.Renderer;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionFireWorksFlight : Match3ActionBase, IDeferredTargetSelection
    {
        private Coords _sourceCoords;
        private Cell _targetCell;
        private readonly int _fireWorksIndex;

        public int UniqueId { get; }
        public bool SettleFound => _targetCell != null;

        public ActionFireWorksFlight(int fireWorksIndex, int sourceTileId, Coords sourceCoords, int fireWorksId, HitWaitParams hitWaitParams)
        {
            _fireWorksIndex = fireWorksIndex;
            _sourceCoords = sourceCoords;
            UniqueId = fireWorksId;
            WaitCondition = ActionWaitConditionFactory.Create(hitWaitParams, sourceTileId, _sourceCoords);
            AffectedCoords.Add(_sourceCoords);
        }
        
        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            proxy.TileTickPlayer.DeleteObjects(obj =>
                obj is FireWorksTarget pt && pt.FireWorksId == UniqueId);
            ReleasedCoords.Add(_sourceCoords);
        }

        protected override string GetMembersString()
        {
            return $"sourceCoords={_sourceCoords} targetCoords={_targetCell.Coords}";
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            var settings = proxy.Settings.GetSettingsForEffect<FireWorksEffectSettings>(FxType.FireWorksFlight);

            AudioProxy.PlaySound(Match3SoundIds.FireworksRocketStartFly);

            var targetPos = _targetCell.Coords.ToUnityVector2();

            targetPos = Tile.GetCenterPointForIfSquareMechanic(grid, _targetCell, targetPos, settings.FireWorksTargetCenterOfSquareMechanic);

            var distance = _sourceCoords.DistanceFrom(targetPos);
            var normalizedDistance = distance / Grid.MaxDistance;
            var duration = Mathf.Lerp(settings.FireWorksMinDuration, settings.FireWorksMaxDuration, normalizedDistance);

            var sourcePos = _sourceCoords.ToUnityVector2();
            var indexOffset = _fireWorksIndex - 1;

            sourcePos.x += indexOffset * settings.FireWorksSourcePositionOffset;
            targetPos.x += indexOffset * settings.FireWorksTargetPositionOffset;
            duration += indexOffset * settings.FireWorksDurationOffset;
            
            proxy.FXRenderer.SpawnFireWorksFlight(sourcePos, targetPos, duration,
                settings, OnFireWorksLaunched, OnFireWorksReached);
            
            return;
            
            void OnFireWorksLaunched()
            {
                ReleasedCoords.Add(_sourceCoords);
            }

            void OnFireWorksReached()
            {
                proxy.TileTickPlayer.BoardObjectFactory.CreateFireWorksTarget(_targetCell.Coords, UniqueId);
            }
        }

        public void SetTargetCell(Cell targetCell)
        {
            _targetCell = targetCell;
        }

        public Coords GetTargetCoords()
        {
            return _targetCell?.Coords ?? Coords.OutOfGrid;
        }
    }
}