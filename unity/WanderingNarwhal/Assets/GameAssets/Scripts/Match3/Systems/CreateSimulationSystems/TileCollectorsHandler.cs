using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic.Tiles;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public static class TileCollectorsHandler
    {
        private static readonly Dictionary<Cell, int> TuktukHits = new();
        private static readonly Dictionary<Cell, int> TntHits = new();
        
        public static readonly List<TileHitReactionHandler.TileReactRecord> DieReactionsCache = new(5);
        
        public static bool IsTileCollector(Coords from, Grid grid)
        {
            var isTnt = grid.TryGetCell(from, out var cell) && cell.IsAnyOf(CellState.Tnt);
            if (isTnt)
            {
                return true;
            }

            var isTukTuk = cell.HasTile() && cell.Tile.Speciality == TileSpeciality.TukTuk;
            return isTukTuk;
        }
            
        public static void HandleTileCollectorsDeath(Grid grid, IRootSimulationHandler events, 
            TileHitReactionHandler reactionHandler, Queue queue, GoalsSystem goalSystem)
        {
            for (var i = DieReactionsCache.Count - 1; i >= 0; i--)
            {
                var item = DieReactionsCache[i];
                var from = item.TileCoords;
                if (!grid.TryGetCell(from, out var cell)) continue;

                if (cell.IsAnyOf(CellState.Tnt))
                {
                    TntCellLayer.HandleTnt(cell, grid, from, events, goalSystem, item.HitWaitParams);
                    DieReactionsCache.RemoveAt(i);
                }

                if (!cell.HasTile() || cell.Tile.Speciality != TileSpeciality.TukTuk) continue;
                {
                    var busyCells = TukTukTile.HandleTukTukDamage(cell, grid, events, queue, item.HitWaitParams);
                    reactionHandler.NewBusyCells ??= new List<Cell>(busyCells.Count);
                    reactionHandler.NewBusyCells.AddRange(busyCells);
                }
                
                DieReactionsCache.RemoveAt(i);
            }
        }
        
        public static bool NotifyCollectorsOnTileDie(Grid grid, IRootSimulationHandler events, Coords coords,
            TileAsset tileAsset, TileKinds tileKinds, HitWaitParams hitWaitParams)
        {
            TuktukHits.Clear();
            TntHits.Clear();

            var isACollectableTile = false;

            var sortedCells = new List<Cell>(grid.Cells);

            sortedCells.Sort((cell1, cell2) =>
            {
                var compareY = cell2.Coords.Y.CompareTo(cell1.Coords.Y);
                return compareY == 0 ? cell1.Coords.X.CompareTo(cell2.Coords.X) : compareY;
            });

            foreach (var cell in sortedCells)
            {
                if (cell.HasTile() && cell.Tile.Speciality == TileSpeciality.TukTuk
                                   && tileAsset == TileAsset.Simple
                                   && cell.Tile.GetParam(TileParamEnum.TukTukColor) == (int)tileKinds)
                {
                    var count = cell.Tile.GetParam(TileParamEnum.TukTukCount);
                    if (count <= 0) continue;
                    isACollectableTile = true;
                    TuktukHits.Add(cell, count);
                }

                if (!cell.IsAnyOf(CellState.Tnt) || cell.TntCount <= 0 ||
                    !TntCellLayer.IsTileMatchTargetType(cell.TntTarget, cell.TntKind, tileAsset, tileKinds))
                    continue;

                isACollectableTile = true;
                TntHits.Add(cell, cell.TntCount);
            }

            if (TntHits.Count > 0)
            {
                var tntCell = TntHits.MinBy(kvp => kvp.Value).Key;
                TntCellLayer.NotifyTnt(events, tntCell, coords, hitWaitParams);
            }

            if (TuktukHits.Count > 0)
            {
                var tuktukCell = TuktukHits.MinBy(kvp => kvp.Value).Key;
                TukTukTile.NotifyTukTuk(events, tuktukCell, coords, hitWaitParams);
            }

            return isACollectableTile;
        }

        public static void NotifyCollectorsOnCellOverlayDie(Grid grid, IRootSimulationHandler handler,
            Coords coords, CellState cellState, HitWaitParams hitWaitParams)
        {
            foreach (var cell in grid.Cells)
            {
                if (!cell.IsAnyOf(CellState.Tnt) || cell.TntCount <= 0) continue;

                switch (cell.TntTarget)
                {
                    case TntTargetType.Grass:
                    {
                        if ((cellState & (CellState.BackOne | CellState.BackDouble | CellState.Petal)) == 0) continue;

                        break;
                    }
                    case TntTargetType.Ivy:
                    {
                        if ((cellState & CellState.Ivy) == 0) continue;

                        break;
                    }
                    default:
                        continue;
                }

                cell.TntCount--;
                if (cell.TntCount <= 0)
                {
                    AddToDieReactionCache(cell);
                }

                handler.AddAction(new ActionReduceTntCount(cell.Coords, coords, cell.SizeX, cell.SizeY, hitWaitParams));
            }
        }
        
        public static int GetTilesLeftToCollect(Grid grid, TileKinds tileKinds)
        {
            const TileAsset tileAsset = TileAsset.Simple;
            var tntCount = 0;
            var tuktukCount = 0;
            foreach (var cell in grid.Cells)
            {
                if (cell.HasTile() && cell.Tile.Speciality == TileSpeciality.TukTuk
                                   && cell.Tile.GetParam(TileParamEnum.TukTukColor) == (int)tileKinds)
                {
                    var count = cell.Tile.GetParam(TileParamEnum.TukTukCount);
                    tuktukCount += count;
                }

                if (!cell.IsAnyOf(CellState.Tnt) || cell.TntCount <= 0) continue;

                if (!TntCellLayer.IsTileMatchTargetType(cell.TntTarget, cell.TntKind, tileAsset, tileKinds)) continue;
                tntCount += cell.TntCount;
            }

            return Math.Max(tntCount, tuktukCount);
        }
        
        public static void AddToDieReactionCache(Cell cell, HitWaitParams hitWaitParams = null)
        {
            DieReactionsCache.Add(new TileHitReactionHandler.TileReactRecord
            {
                TileCoords = cell.Coords,
                HitWaitParams = hitWaitParams
            });
        }
    }
}