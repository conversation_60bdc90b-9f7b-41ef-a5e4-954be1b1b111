namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public class ActionSkunkExplosion : Match3ActionBase
    {
        private readonly SkunkActiveBoardObjectPredicate _activeObjectPredicate;
        private readonly int _skunkId;
        private readonly Cell _targetCell;
        private readonly Coords _sourceCoords;

        public ActionSkunkExplosion(int skunkId, Cell cell, Coords sourceCoords)
        {
            _activeObjectPredicate = new SkunkActiveBoardObjectPredicate
            {
                SkunkId = skunkId
            };
            _skunkId = skunkId;
            _targetCell = cell;
            _sourceCoords = sourceCoords;
            AffectedCoords.Add(_targetCell.Coords);
            AffectedCoords.Add(_sourceCoords);
        }

        protected override bool CanExecute(Grid grid, PlaySimulationActionProxy proxy)
        {
            return proxy.TileTickPlayer.AnyActiveObjectSatisfiesPredicate(_activeObjectPredicate) &&
                   base.CanExecute(grid, proxy);
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ReleaseObjects(proxy);
        }

        private void ReleaseObjects(PlaySimulationActionProxy proxy)
        {
            proxy.TileTickPlayer.DeleteObjects(obj => obj is SkunkTarget st && st.SkunkId == _skunkId);
            ReleasedCoords.Add(_targetCell.Coords);
            ReleasedCoords.Add(_sourceCoords);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ReleaseObjects(proxy);
        }
    }
}