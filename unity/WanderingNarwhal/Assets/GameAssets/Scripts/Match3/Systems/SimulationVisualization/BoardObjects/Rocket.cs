using System.Collections.Generic;
using BBB.Match3.Renderer;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public class Rocket : BoardObjectBase
    {
        private const float LifeTime = 4f;

        public readonly int LineBreakerId;
        private readonly List<RocketTarget> _rocketTargets;

        private readonly Vector2 _direction;
        private readonly M3Settings _settings;

        private LineBreakerEffect _view;

        private float _timer = 0f;

        private float _speed;

        public Rocket(int lbId, List<RocketTarget> rocketTargets, Vector2 direction, Vector2 startPosition, M3Settings settings, int id) : base(id)
        {
            LineBreakerId = lbId;
            _rocketTargets = rocketTargets;
            _direction = direction;
            Position = startPosition;
            StartPosition = startPosition;

            _settings = settings;

            _speed = _settings.RocketSpeed;

            _timer = LifeTime;
        }

        public override void Update(float deltaTime, PlaySimulationActionProxy proxy)
        {
            _speed += Mathf.Abs(_settings.RocketAcceleration * deltaTime);
            Position += _speed * deltaTime * _direction;

            if (_timer > 0f)
            {
                _timer -= deltaTime;

                if (_timer <= 0f)
                {
                    Delete(proxy);
                }
                else
                {
                    foreach (var target in _rocketTargets)
                    {
                        if (target.Deleted)
                            continue;

                        if (OverlappedOrPassed(target.Coords, proxy))
                        {
                            target.Delete(proxy);
                        }
                    }
                }
            }
        }

        public override void UpdateView(PlaySimulationActionProxy proxy, bool isSpawnView)
        {
            if (_view != null)
            {
                _view.transform.localPosition = proxy.GridController.ToLocalPosition(Position);
            }
        }

        public override void SpawnView(RendererContainers rendererContainers)
        {
            _view = rendererContainers.SpawnFx<LineBreakerEffect>(FxType.LineBreaker);
            _view.Init(_direction);
        }

        protected override void ReleaseView(PlaySimulationActionProxy playSimulationActionProxy)
        {
            if (_view != null && _view.gameObject != null)
            {
                _view.gameObject.Release();
            }

            _view = null;
        }

        public override void AfterDeleted(PlaySimulationActionProxy proxy)
        {
            foreach (var target in _rocketTargets)
            {
                if (target.Deleted)
                    continue;

                target.Delete(proxy);
            }

            proxy.TileTickPlayer.AddToTrashBin(this);
        }

        public override bool Overlaps(Vector2 worldPos)
        {
            return _view.OverlapPoint(worldPos);
        }

        private bool Passed(Vector2 hitGridPos)
        {
            var dir = _direction;
            var pos = Position;

            var dirToTarget = hitGridPos - StartPosition;

            var dotProduct = Vector2.Dot(dir, dirToTarget);

            //if vectors are perpendicular, dot product will be 0
            //if they are collinear but face opposite directions, dot product will be negative
            //we need to make sure this rocket affects only tiles on its side, so we need to
            //guarantee dirToTarget and dir will have same direction

            if (dotProduct > 0f || dirToTarget.magnitude == 0)
            {
                bool xAxisDir = dir.x != 0f;
                var nonZeroDirComponent = xAxisDir ? dir.x : dir.y;
                var borderComponentValue = xAxisDir ? hitGridPos.x : hitGridPos.y;
                var posComponentValue = xAxisDir ? pos.x : pos.y;

                return nonZeroDirComponent > 0f ? posComponentValue >= borderComponentValue : posComponentValue <= borderComponentValue;
            }

            return false;
        }

        public bool OverlappedOrPassed(Coords coords, PlaySimulationActionProxy proxy)
        {
            var hitGridPos = coords.ToUnityVector2();
            var dirToRocket = Position - hitGridPos;
            dirToRocket.Normalize();
            var collisionOffset = dirToRocket * proxy.Settings.TileCollisionRadius;
            var hitWorldPos = proxy.GridController.ToDisplacedWorldPosition(hitGridPos + collisionOffset);
            return Overlaps(hitWorldPos) || Passed(hitGridPos);
        }
    }
}