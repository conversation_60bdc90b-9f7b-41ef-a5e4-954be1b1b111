using System;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.AssistSystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public partial class AssistSystem
    { 
        private readonly PossiblePutsPool PossbilePutsPool = new (89);
                                                      
        //possible put is an entity that defines coordinates and tile kind
        //to put there after definition (by replacing the existing one if needed)
        private readonly List<PossiblePut> _detectedPossiblePuts = new ();
        private readonly List<PossiblePut> _putsToApply = new ();

        private readonly HashSet<SortedCoordsSet> _duplicatePutsFilter = new ();

        public void CleanDuplicatePutSet()
        {
            _duplicatePutsFilter.Clear();
        }

        private void PrepareAutoMatches(Grid grid, List<TileKinds> usedKinds, AssistInputAutomatches inputInstance)
        {
            //clean up
            foreach (var put in _detectedPossiblePuts)
                PossbilePutsPool.Release(put);
            
            _detectedPossiblePuts.Clear();
            
            _putsToApply.Clear();
            
            if (inputInstance.Limit <= 0)
                return;
            
            //collecting information of all possible puts
            foreach (var cell in grid.Cells)
            {
                if (ReferenceEquals(cell.Tile, null)) continue;
                
                //for each undefined tile we detect the possible puts
                if (cell.Tile.Kind == TileKinds.Undefined)
                {
                    DetectPossiblePuts(grid, cell.Coords, usedKinds);
                }
            }

            //if any possible puts detected, we need to pick which one is the higher priority
            if (_detectedPossiblePuts.Count > 0)
            {
                //for each put we analyze consequences
                foreach (var possiblePut in _detectedPossiblePuts)
                {
                    possiblePut.AnalyzeConsequences(grid);
                }

                //if this time we allow to destroy only assist markers, remove any puts that do not affect assist markers
                if (inputInstance.AssistMarkerOnly)
                {
                    _detectedPossiblePuts.RemoveAll(put => put.AssistMarkersCount == 0);
                }
                else//otherwise remove all puts that do not affect goals
                {
                    _detectedPossiblePuts.RemoveAll(put => put.Score(inputInstance.GoalValueWeights) <= 0f && !put.HasBoosters());
                }

                //if there are any puts destroying assist markers we pick the as highest priority
                //otherwise we sort by highest score of consequences
                if (_detectedPossiblePuts.Count > 0)
                {
                    var sortedPuts = new List<PossiblePut>(_detectedPossiblePuts);

                    sortedPuts.Sort((put1, put2) =>
                    {
                        var result = put2.AssistMarkersCount.CompareTo(put1.AssistMarkersCount);
                        if (result == 0)
                        {
                            result = put2.Score(inputInstance.GoalValueWeights).CompareTo(put1.Score(inputInstance.GoalValueWeights));
                        }
                        if (result == 0)
                        {
                            result = put2.TotalLength.CompareTo(put1.TotalLength);
                        }
                        return result;
                    });

                    foreach (var put in sortedPuts)
                    {
                        var shouldContinue = false;
                        foreach (var putFromList in _putsToApply)
                        {
                            if (!putFromList.Overlaps(put) && !putFromList.IsUnitDistanceFrom(grid, put)) continue;
                            
                            shouldContinue = true;
                            break;
                        }

                        if (shouldContinue)
                            continue;

                        _putsToApply.Add(put);

                        if (_putsToApply.Count >= inputInstance.Limit)
                            break;
                    }
                }
                
                //if there are too much puts picked, we cut the tail
                if (_putsToApply.Count > inputInstance.Limit)
                {
                    _putsToApply.RemoveRange(inputInstance.Limit, _putsToApply.Count-inputInstance.Limit);
                }
                
                //then decrease the limit of puts for this turn, if there are fw left, they can be created on the next turn of the same move
                inputInstance.DecreaseLimit(_putsToApply.Count);
            }
        }

        private void DetectPossiblePuts(Grid grid, Coords coords, List<TileKinds> usedKind)
        {
            //to detect a possible put for a particular cell we check if there will be a match if we put something into it
            
            //far bottom and close bottom
            DetectPossiblePuts(grid, new Coords(coords.X, coords.Y - 2), 
                new Coords(coords.X, coords.Y - 1), coords);
            
            //far top and close top
            DetectPossiblePuts(grid, new Coords(coords.X, coords.Y + 2), 
                new Coords(coords.X, coords.Y + 1), coords);
            
            //close bottom and close top
            DetectPossiblePuts(grid, new Coords(coords.X, coords.Y - 1), 
                new Coords(coords.X, coords.Y + 1), coords);

            if (coords.Y != grid.Height - 1) //for highest row we do not detect vertical possible puts, they do not feel right in the game
            {
                //far left and close left
                DetectPossiblePuts(grid, new Coords(coords.X - 2, coords.Y), 
                    new Coords(coords.X - 1, coords.Y), coords);
            
                //far right and close right
                DetectPossiblePuts(grid, new Coords(coords.X + 2, coords.Y), 
                    new Coords(coords.X + 1, coords.Y), coords);
            
                //close left and close right
                DetectPossiblePuts(grid, new Coords(coords.X - 1, coords.Y), 
                    new Coords(coords.X + 1, coords.Y), coords);
            }
            
            //try square match
            DetectPossibleSquarePuts(grid, coords, usedKind);
        }

        private void DetectPossibleSquarePuts(Grid grid, Coords coords, List<TileKinds> usedKinds)
        {
            TileKinds? targetTileKind = null;
            Span<Coords> putCoords = stackalloc Coords[4];
            putCoords[0] = coords;
            var hasMatch = true;
            for (var v = 0; v <= 3; ++v) //4 variant of the square match based on the current coord
            {
                hasMatch = true;
                for (var i = 1; i <= 3; i++)
                {
                    var neighbourCoords = MatchHelper.SquareCoordsFromIndex((v + i) % 4, coords);
                    putCoords[i] = neighbourCoords;
                    var hasCell = grid.TryGetCell(neighbourCoords, out var neighbourCell);
                    if (hasCell
                        && neighbourCell.HasTile()
                        && neighbourCell.IsBaseCellMatchable()
                        && (neighbourCell.Tile.IsMatchable() || neighbourCell.Tile.Kind == TileKinds.Undefined))
                    {
                        if (neighbourCell.Tile.Kind == TileKinds.Undefined)
                            continue;

                        if (!targetTileKind.HasValue)
                        {
                            targetTileKind = neighbourCell.Tile.Kind;
                        }
                        else if (targetTileKind != neighbourCell.Tile.Kind)
                        {
                            hasMatch = false;
                            break;
                        }
                    }
                    else
                    {
                        hasMatch = false;
                        break;
                    }
                }
                
                if (hasMatch)//found square match
                    break;
            }

            if (!hasMatch)
                return;

            targetTileKind ??= usedKinds.DeterministicRandomInSelf();
                
            var possiblePut = PossbilePutsPool.Spawn();
            var matches = GetMatches(grid, targetTileKind.Value, putCoords.ToArray());
            if (matches == null)
                return;
            var coordsSet = new SortedCoordsSet(matches);
            if (possiblePut != null && !_duplicatePutsFilter.Contains(coordsSet))
            {
                possiblePut.AddTileChange(coords, targetTileKind.Value);
                for (var i = 1; i < putCoords.Length; ++i)
                {
                    if (grid.TryGetCell(putCoords[i], out Cell cell) && cell.Tile.Kind == TileKinds.Undefined)
                    {
                        possiblePut.AddTileChange(putCoords[i], targetTileKind.Value);
                    }
                }
                
                var assistMarkersCount = grid.CountAssistMarkers(matches);
                possiblePut.AddAutoMatch(targetTileKind.Value, assistMarkersCount, matches);
                
                _detectedPossiblePuts.Add(possiblePut);
                _duplicatePutsFilter.Add(coordsSet);
            }
        }

        private static Match[] GetMatches(Grid grid, TileKinds kind, params Coords[] coords)
        {
            Span<bool> tilesToRevert = stackalloc bool[coords.Length];
            for (var i = 0; i < coords.Length; ++i)
            {
                var cell = grid.GetCell(coords[i]);
                var isUndefined = cell.Tile.Kind == TileKinds.Undefined;
                tilesToRevert[i] = isUndefined;
                if (isUndefined)
                {
                    cell.Tile.SetKind(kind);
                }
            }

            var matches = new HashSet<Match>();
            SearchMatchesSystem.FindMatchForCell(grid, grid.GetCell(coords[0]), ref matches);

            for (var i = 0; i < coords.Length; ++i)
            {
                if (!tilesToRevert[i]) continue;
                
                var cell = grid.GetCell(coords[i]);
                cell.Tile.SetKind(TileKinds.Undefined);
            }
            
            if (matches == null)
            {
                return null;
            }
            
            var matchArray = new Match [matches.Count];
            
            var index = 0;
            foreach (var match in matches)
            {
                matchArray[index++] = match;
            }
            
            return matchArray;
        }

        private void DetectPossiblePuts(Grid grid, Coords firstCoords, Coords secondCoords, Coords coords)
        {
            if (grid.IsSameKind(firstCoords, secondCoords, out var kind))
            {
                if (!kind.IsColored() || !grid.HasSpecAt(firstCoords, TileSpeciality.None) || !grid.HasSpecAt(secondCoords, TileSpeciality.None))
                    return;
                
                var possiblePut = PossbilePutsPool.Spawn();
                var matches = GetMatches(grid, kind, coords);
                if (matches == null)
                    return;
                var coordsSet = new SortedCoordsSet(matches);
                    
                //duplicate puts filter is needed to not create the same put twice (if it creates two matches)
                if (possiblePut == null || _duplicatePutsFilter.Contains(coordsSet)) return;
                
                //put contains information of tiles it needs to change
                possiblePut.AddTileChange(coords, kind);
                var assistMarkersCount = grid.CountAssistMarkers(matches);
                //it also contains information about automatch it will create
                possiblePut.AddAutoMatch(kind, assistMarkersCount, matches);
                _detectedPossiblePuts.Add(possiblePut);
                _duplicatePutsFilter.Add(coordsSet);
            }
            // if the the first or second neighbour is undefined, we try to define subject tile and the neighbour
            // to the kind of the other neighbour
            else if(UndefinedAt(grid, firstCoords))
            {
                if (!grid.TryGetCell(secondCoords, out var secondCell)
                    || ReferenceEquals(secondCell.Tile, null)
                    || !secondCell.Tile.Kind.IsColored()
                    || secondCell.Tile.Speciality != TileSpeciality.None) return;
                
                var secondKind = secondCell.Tile.Kind;
                var possiblePut = PossbilePutsPool.Spawn();
                var matches = GetMatches(grid, secondKind, firstCoords);
                
                if (matches == null)
                    return;
                
                var coordsSet = new SortedCoordsSet(matches);
                
                if (possiblePut == null || _duplicatePutsFilter.Contains(coordsSet)) return;
                
                possiblePut.AddTileChange(coords, secondKind);
                possiblePut.AddTileChange(firstCoords, secondKind);
                var assistMarkersCount = grid.CountAssistMarkers(matches);
                possiblePut.AddAutoMatch(secondKind, assistMarkersCount, matches);
                _detectedPossiblePuts.Add(possiblePut);
                _duplicatePutsFilter.Add(coordsSet);
            }
            else if(UndefinedAt(grid, secondCoords))
            {
                if (!grid.TryGetCell(firstCoords, out var firstCell)
                    || ReferenceEquals(firstCell.Tile, null)
                    || !firstCell.Tile.Kind.IsColored()
                    || firstCell.Tile.Speciality != TileSpeciality.None) return;
                
                var firstKind = firstCell.Tile.Kind;
                var possiblePut = PossbilePutsPool.Spawn();
                var matches = GetMatches(grid, firstKind, secondCoords);
                
                if (matches == null)
                    return;
                
                var coordsSet = new SortedCoordsSet(matches);
                
                if (possiblePut == null || _duplicatePutsFilter.Contains(coordsSet)) return;
                
                possiblePut.AddTileChange(coords, firstKind);
                possiblePut.AddTileChange(secondCoords, firstKind);
                var assistMarkersCount = grid.CountAssistMarkers(matches);
                possiblePut.AddAutoMatch(firstKind, assistMarkersCount, matches);
                _detectedPossiblePuts.Add(possiblePut);
                _duplicatePutsFilter.Add(coordsSet);
            }
        }

        private bool UndefinedAt(Grid grid, Coords someCoord)
        {
            return grid.TryGetCell(someCoord, out var someCell) && someCell.Tile is { Kind: TileKinds.Undefined };
        }

        /// <summary>
        /// this method is called after tile definition in order to apply puts and make automatches to match on the next turn
        /// </summary>
        private void TryApplyAutoMatches(Grid grid, IRootSimulationHandler simHandler)
        {
            foreach (var putToApply in _putsToApply)
            {
                foreach (var tileChange in putToApply.TileChanges)
                    if(grid.TryGetCell(tileChange.Item1, out var cell) && !ReferenceEquals(cell.Tile, null))
                    {
                        cell.Tile.SetKind(tileChange.Item2);
                        simHandler?.OnTileKindDefined(cell.Tile.Id, tileChange.Item2);
                    }
                putToApply.ApplyToGrid(grid);
            }
            
            _putsToApply.Clear();
        }
    }
}