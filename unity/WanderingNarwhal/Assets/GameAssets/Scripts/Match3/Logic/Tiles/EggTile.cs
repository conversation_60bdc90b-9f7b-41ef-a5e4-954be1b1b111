using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Core;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class EggTile : Tile
    {
        private const int BirdSpawnCount = 1; //1 Bird
        private (long, int value) _assistState = ((long) GoalType.Bird, DefaultHp);
        private const string EggOne = "EggOne";
        private const string EggTwo = "EggTwo";

        public EggTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Egg;
            State |= TileState.Egg;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.EggLayerCount) + BirdSpawnCount;
            yield return _assistState;
        }

        public override void AddMandatoryParamsTile()
        {
            if (GetParam(TileParamEnum.EggLayerCount) <= 0)
            {
                BDebug.LogError(LogCat.Match3, "Spawned egg tile doesn't have egg layer parameter!");
                SetParam(TileParamEnum.EggLayerCount, 1);
            }

            SetParam(TileParamEnum.TileToSpawnFromReaction, (int) TileAsset.Bird);
            SetParam(TileParamEnum.TileCreateCountForReaction, BirdSpawnCount);
            base.AddMandatoryParamsTile();
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var eggLayerCount = GetParam(TileParamEnum.EggLayerCount);

            if (eggLayerCount >= 1)
            {
                yield return EggOne;
            }

            if (eggLayerCount >= 2)
            {
                yield return EggTwo;
            }
        }
    }
}