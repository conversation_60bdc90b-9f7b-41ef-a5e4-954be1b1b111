using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class HiveTile : Tile
    {
        private static readonly List<Coords> AllBeeHives = new();
        private const int BeeSpawnCount = 1;
        private const string ExhaustedHive = "ExhaustedHive";
        private const string Hive = "Hive";

        public HiveTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams = null)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Hive;
            State |= TileState.Hive;
            AddMandatoryParamsTile();
        }

        public override void AddMandatoryParamsTile()
        {
            SetParam(TileParamEnum.TileToSpawnFromReaction, (int) TileAsset.Bee);
            SetParam(TileParamEnum.TileCreateCountForReaction, BeeSpawnCount);
            base.AddMandatoryParamsTile();
        }

        public override bool CheckApplicability(BoosterItem boosterItem, Cell cell, Grid grid)
        {
            if ((State & (TileState.ChainMod | TileState.IceCubeMod | TileState.SandMod)) != 0) return true;
            return GetParam(TileParamEnum.BeeHiveOutOfBeesFlag) <= 0;
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }
        
        public override bool DisallowCellBackgroundSpawn()
        {
            return true;
        }
        
        public override bool PreventBackgroundInteraction()
        {
            return true;
        }
        
        public override bool IsIndestructibleBlocker()
        {
            return true;
        }
        
        public static int CanHitBeehive(Coords coords, Grid grid, GoalsSystem goalSystem,
            TileHitReactionHandler reactionHandler)
        {
            var beeHiveCell = grid.GetCell(coords);

            if (beeHiveCell != null && beeHiveCell.Tile != null &&
                beeHiveCell.Tile.GetParam(TileParamEnum.BeeHiveOutOfBeesFlag) == 1)
            {
                return 0;
            }

            var count = goalSystem.GetLeftGoalCount(GoalType.Bee);
            if (count <= 0) return 0;

            foreach (var action in TileSpawnHandler.DelayedSpawnReactions)
            {
                if (action.Tile.Speciality == TileSpeciality.Bee)
                {
                    count--;
                }
            }

            foreach (var action in reactionHandler.DieReactionsCache)
            {
                if (action.Speciality == TileSpeciality.Bee)
                {
                    count--;
                }
            }

            foreach (var cell in grid.Cells)
            {
                if (!cell.Tile.IsNull() && cell.Tile.Speciality == TileSpeciality.Bee)
                {
                    count--;
                }
            }

            return count;
        }
        
        public static void MarkHivesOnGridOutOfBeesIfNeeded(Grid grid, IRootSimulationHandler events,
            GoalsSystem goalSystem, TileHitReactionHandler reactionHandler)
        {
            var isOutOfBees = false;
            var targetLeft = goalSystem.GetLeftGoalCount(GoalType.Bee);

            if (targetLeft > 0)
            {
                var spawnedCount = 0;

                foreach (var m in TileSpawnHandler.DelayedSpawnReactions)
                {
                    if (m.Tile.IsAnyOf(TileState.BeeMod))
                    {
                        spawnedCount++;
                    }
                }

                foreach (var cell in grid.Cells)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.BeeMod))
                    {
                        spawnedCount++;
                    }
                }

                if (spawnedCount >= targetLeft)
                {
                    isOutOfBees = true;
                }
            }
            else
            {
                isOutOfBees = true;
            }

            if (isOutOfBees)
            {
                foreach (var cell in grid.Cells)
                {
                    if (!cell.Tile.IsNull() && cell.Tile.IsAnyOf(TileState.HiveMod))
                    {
                        AllBeeHives.Add(cell.Coords);
                    }
                }
            }

            if (AllBeeHives.Count == 0) return;
            reactionHandler.NewBusyCells ??= new List<Cell>(AllBeeHives.Count);
            events.AddAction(new ActionSyncCoords(new List<Coords>(AllBeeHives)));

            foreach (var beeHiveCoords in AllBeeHives)
            {
                var cell = grid.GetCell(beeHiveCoords);
                cell.Tile.SetParam(TileParamEnum.BeeHiveOutOfBeesFlag, 1);
                events.AddAction(new ActionChangeTileParam(cell.Tile.Id, cell.Coords,
                    new List<(TileParamEnum, int)> { new(TileParamEnum.BeeHiveOutOfBeesFlag, 1) }));
                reactionHandler.NewBusyCells.Add(cell);
            }

            AllBeeHives.Clear();
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            if (GetParam(TileParamEnum.BeeHiveOutOfBeesFlag) == 1)
            {
                yield return ExhaustedHive;
            }
            
            yield return Hive;
        }
    }
}