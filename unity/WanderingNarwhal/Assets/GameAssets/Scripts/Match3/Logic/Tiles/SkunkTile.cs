using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SkunkTile : Tile
    {
        private const string Skunk = "Skunk";
        
        public SkunkTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Skunk;
            State |= TileState.Skunk;
            GeneralizedLayer = Skunk;
            AddMandatoryParamsTile();
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (simulationContext.CellsToDamageQueue == null)
            {
                return true;
            }
            
            var removeRandomTileQueue = SpecialTileSystem.RemoveWithSkunkHit(this, hitContext.Hit, hitContext.Cell,
                hitContext.HitWaitParams, simulationContext.SettleTileSystem, simulationContext.Handler);
            
            if (removeRandomTileQueue != null)
            {
                simulationContext.CellsToDamageQueue.Append(removeRandomTileQueue);
            }
            return true;
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }
        
        public override bool DisallowCellBackgroundSpawn()
        {
            return true;
        }
        
        public override bool PreventBackgroundInteraction()
        {
            return true;
        }
        
        public override bool IsIndestructibleBlocker()
        {
            return true;
        }
    }
}