using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class MetalBarTile : BarTile
    {
        private (long, int value) _assistState = ((long) GoalType.MetalBar, DefaultHp);
        private const string MetalBar = "MetalBar";
        
        public MetalBarTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.MetalBar;
            AllowedDamageSource = DamageSource.PowerUp | DamageSource.Whirlpool | DamageSource.Dynamite |
                                  DamageSource.UsableBoost | DamageSource.SuperBoost | DamageSource.Skunk |
                                  DamageSource.FireWorks;
            State |= TileState.MetalBar;
            GeneralizedLayer = MetalBar;
            AddMandatoryParamsTile();
            BarOrientation = TileParamEnum.MetalBarOrientation;
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
        
        public static void HandleMetalBar(Grid grid, IRootSimulationHandler events)
        {
            foreach (var c in grid.Cells)
            {
                var mainCell = c.GetMainCellReference(out _);
                if (!mainCell.Tile.IsNull() && mainCell.Tile.Speciality == TileSpeciality.MetalBar) continue;

                if (!c.IsAnyOf(CellState.MetalBar) && !c.MetalBarStatus) continue;
                c.MetalBarStatus = false;
                c.Remove(CellState.MetalBar);
                events.AddAction(new ActionFreeUpMetalBarCells(c.Coords));
            }
        }
    }
}