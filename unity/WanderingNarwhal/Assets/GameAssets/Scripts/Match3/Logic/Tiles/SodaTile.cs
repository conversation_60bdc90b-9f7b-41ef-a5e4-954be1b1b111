using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SodaTile : Tile
    {
        private const int NumberOfBottles = 4;
        private (long, int value) _assistState = ((long) GoalType.SodaBottle, DefaultHp);
        private const string Soda = "Soda";

        public SodaTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams) 
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Soda;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.Soda;
            GeneralizedLayer = Soda;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            if (TryApplySodaTileDamage(simulationContext.InputParams, hitContext.MainCell, hitContext.Hit,
                    hitContext.HitWaitParams.DamageSource, simulationContext.Handler))
            {
                return base.TryApplyAdjacentDamage(simulationContext, hitContext);
            }

            var reducedAdjacentHp = GetParam(TileParamEnum.SodaBottlesCount);
            simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.Cell.Coords,
                new List<(TileParamEnum, int)> {new(TileParamEnum.AdjacentHp, reducedAdjacentHp)},
                hitContext.HitWaitParams));
            
            return true;
        }

        private bool TryApplySodaTileDamage(SimulationInputParams inputParams, Cell cell, Hit hit,
            DamageSource damageSource, IRootSimulationHandler handler)
        {
            if (inputParams.InitialLoop || !HasParam(TileParamEnum.SodaColors)) return false;
            var sodaCount = GetParam(TileParamEnum.SodaBottlesCount);
            var count = sodaCount;
            var state = GetParam(TileParamEnum.SodaColors);
            var damageColorNum = SodaTileLayer.ColorToEncodedNum(hit.SourceKind);
            int subState;

            //Using 4 in all loops as the maxValue for the number of Soda Bottles are not defined
            if ((damageSource & DamageSource.AdjacentGeneral) != 0 && damageColorNum != 0)
            {
                for (var i = 0; i < NumberOfBottles; i++)
                {
                    subState = SodaTileLayer.GetColorNumFromState(state, i);
                    if (subState != damageColorNum) continue;
                    state = SodaTileLayer.SetColorNumInState(state, 0, i);
                    count--;
                    break;
                }
            }
            else
            {
                var randomIndex = RandomSystem.Next(NumberOfBottles);
                var nonZeroSubStateIndex = new List<int>();
                var bottleRemoved = false;
                for (var i = 0; i < NumberOfBottles; i++)
                {
                    subState = SodaTileLayer.GetColorNumFromState(state, i);
                    if (subState == 0) continue;
                    if (i == randomIndex)
                    {
                        state = SodaTileLayer.SetColorNumInState(state, 0, i);
                        count--;
                        bottleRemoved = true;
                        break;
                    }

                    nonZeroSubStateIndex.Add(i);
                }

                if (!bottleRemoved && nonZeroSubStateIndex.Count > 0)
                {
                    randomIndex = RandomSystem.Next(nonZeroSubStateIndex.Count);
                    state = SodaTileLayer.SetColorNumInState(state, 0, nonZeroSubStateIndex[randomIndex]);
                    count--;
                }
            }

            if (count >= sodaCount) return false;
            SetParam(TileParamEnum.SodaColors, state);
            SetParam(TileParamEnum.SodaBottlesCount, count);

            var tileParamList = new List<(TileParamEnum, int)>
            {
                new(TileParamEnum.AdjacentHp, count),
                new(TileParamEnum.SodaBottlesCount, count),
                new(TileParamEnum.SodaColors, state)
            };

            handler.AddAction(new ActionChangeTileParam(Id, cell.Coords,
                tileParamList, hit.GetHitParams()));
            return true;
        }

        protected override bool CanBeDamagedBy(DamageSource damageSource, TileKinds damageTileKind, DamageSource totalAllowedDamageSource)
        {
            if (IsSameTileKindDamage(damageSource, totalAllowedDamageSource) && HasParam(TileParamEnum.SodaColors))
            {
                var damageColorNum = SodaTileLayer.ColorToEncodedNum(damageTileKind);
                var state = GetParam(TileParamEnum.SodaColors);
                if (!SodaTileLayer.IsColorStateExistInState(state, damageColorNum))
                {
                    return false;
                }
            }

            return base.CanBeDamagedBy(damageSource, damageTileKind, totalAllowedDamageSource);
        }
    }
}