using BBB;
using BBB.Match3;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BigMonkeyTile : MonkeyTile
    {
        public const string BigMonkey = "BigMonkey";
        
        public BigMonkeyTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.BigMonkey;
            State = (State & ~TileState.Monkey) | TileState.BigMonkey;
            GeneralizedLayer = BigMonkey;
        }

        protected override bool CanDieRepeatedly()
        {
            return true;
        }
    }
}