using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SafeTile : LayeredTile
    {
        private (long, int value) _assistState = ((long) GoalType.Safe, DefaultHp);
        private const string Safe = "Safe";
        
        public SafeTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams) 
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Safe;
            AllowedDamageSource = DamageSource.PowerUp | DamageSource.Whirlpool | DamageSource.Dynamite |
                                  DamageSource.UsableBoost | DamageSource.SuperBoost | DamageSource.Skunk |
                                  DamageSource.FireWorks;
            State |= TileState.Safe;
            GeneralizedLayer = Safe;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
    }
}