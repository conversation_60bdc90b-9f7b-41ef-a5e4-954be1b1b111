using BBB;
using BBB.Match3;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BananaTile : Tile
    {
        private const int BananaHp = 1;
        private static readonly (long, int) AssistState = ((long) GoalType.Banana, BananaHp);
        private const string Banana = "Banana";

        public BananaTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Banana;
            State |= TileState.Banana;
            GeneralizedLayer = Banana;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            yield return AssistState;
        }
    }
}