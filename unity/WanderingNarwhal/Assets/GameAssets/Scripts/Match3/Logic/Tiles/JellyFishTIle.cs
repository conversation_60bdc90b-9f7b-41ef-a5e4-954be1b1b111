using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class JellyFishTIle : Tile
    {
        private const int JellyFishMin = 1;
        private const int JellyFishMax = 8;
        private (long, int value) _assistState = ((long) GoalType.JellyFish, DefaultHp);
        private const string JellyFish = "JellyFish";

        public JellyFishTIle(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.JellyFish;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.JellyFish;
            GeneralizedLayer = JellyFish;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);
            if (adjacentHp <= 0)
            {
                return false;
            }
            var reducedAdjacentHp = adjacentHp - 1;

            if (reducedAdjacentHp <= 0)
            {
                return base.TryApplyAdjacentDamage(simulationContext, hitContext);
            }

            var currentColor = GetParam(TileParamEnum.JellyFishColor);
            var randomColor = RandomSystem.Next(JellyFishMin, JellyFishMax);

            while (randomColor == currentColor ||
                   !simulationContext.InputParams.UsedKinds.Contains((TileKinds)randomColor))
            {
                randomColor = RandomSystem.Next(JellyFishMin, JellyFishMax);
            }

            SetParam(TileParamEnum.AdjacentHp, reducedAdjacentHp);
            SetParam(TileParamEnum.JellyFishColor, randomColor);

            var tileParamList = new List<(TileParamEnum, int)>
            {
                new(TileParamEnum.AdjacentHp, reducedAdjacentHp),
                new(TileParamEnum.JellyFishColor, randomColor)
            };

            simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.MainCell.Coords,
                tileParamList, hitContext.Hit.GetHitParams()));

            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }
        
        protected override bool CanBeDamagedBy(DamageSource damageSource, TileKinds damageTileKind, DamageSource totalAllowedDamageSource)
        {
            if (IsSameTileKindDamage(damageSource, totalAllowedDamageSource) && HasParam(TileParamEnum.JellyFishColor))
            {
                var state = GetParam(TileParamEnum.JellyFishColor);
                if (damageTileKind != (TileKinds)state)
                {
                    return false;
                }
            }
            return base.CanBeDamagedBy(damageSource, damageTileKind, totalAllowedDamageSource);
        }
    }
}