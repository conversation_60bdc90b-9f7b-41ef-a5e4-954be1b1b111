using BBB;
using BBB.Match3;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class MoneyBagTile : Tile
    {
        private const int MoneyBagHp = 1;
        private static readonly (long, int) AssistState = ((long) GoalType.MoneyBag, MoneyBagHp);
        
        private const string MoneyBag = "MoneyBag";
        private const string VaseOne = "VaseOne";
        private const string VaseTwo = "VaseTwo";
        private const string VaseThree = "VaseThree";
        
        public MoneyBagTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.MoneyBag;
            State |= TileState.MoneyBag;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            yield return AssistState;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            if (IsNoneOf(TileState.VaseMod))
            {
                yield return MoneyBag;
            }
        }

        public override IEnumerable<string> GetGeneralizedLayersModState()
        {
            if (IsAnyOf(TileState.VaseMod))
            {
                var vaseLayerCount = GetParam(TileParamEnum.VaseLayerCount);
                
                if (vaseLayerCount >= 1)
                {
                    yield return VaseOne;
                }

                if (vaseLayerCount >= 2)
                {
                    yield return VaseTwo;
                }

                if (vaseLayerCount >= 3)
                {
                    yield return VaseThree;
                }
            }
        }
    }
}