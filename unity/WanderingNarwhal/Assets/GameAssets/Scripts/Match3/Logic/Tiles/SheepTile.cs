using BBB;
using BBB.Match3;
using System.Collections.Generic;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class SheepTile : Tile
    {
        private (long, int value) _assistState = ((long)GoalType.Sheep, DefaultHp);
        private const int SheepHp = 3;
        private const string SheepOne = "SheepOne";
        private const string SheepTwo = "SheepTwo";
        private const string SheepThree = "SheepThree";

        public SheepTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams) 
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Sheep;
            State |= TileState.Sheep;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        protected override int DefaultAdjacentHp()
        {
            return (State & TileState.Sheep) != 0 ? SheepHp : 0;
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);

            if (adjacentHp >= 1)
            {
                yield return SheepOne;
            }

            if (adjacentHp >= 2)
            {
                yield return SheepTwo;
            }

            if (adjacentHp >= 3)
            {
                yield return SheepThree;
            }
        }
    }
}