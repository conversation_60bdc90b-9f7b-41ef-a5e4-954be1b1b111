using System;
using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;
using GameAssets.Scripts.Match3.Settings;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class GiantPinataTile : LayeredTile
    {
        private const int MaxSpawnCount = 4;
        private (long, int value) _assistState = ((long)GoalType.GiantPinata, DefaultHp);
        private const string GiantPinata = "GiantPinata";

        public GiantPinataTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.GiantPinata;
            AllowedDamageSource = DamageSource.PowerUp | DamageSource.Whirlpool | DamageSource.Dynamite |
                                  DamageSource.UsableBoost | DamageSource.SuperBoost | DamageSource.Skunk |
                                  DamageSource.FireWorks;
            State |= TileState.GiantPinata;
            GeneralizedLayer = GiantPinata;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var adjacentHp = GetParam(TileParamEnum.AdjacentHp);
            if (adjacentHp <= 0)
            {
                return false;
            }

            var reducedAdjacentHp = adjacentHp - 1;
            if (GetParam(TileParamEnum.TileCreateCountForReaction) == 0)
            {
                var tileList = GetWeightedRandomOutcome(simulationContext.InputParams.Settings.GiantPinataConfiguration);
                var tilesToSpawn = PackTileAssetsDynamic(tileList);

                var tileParamList = new List<(TileParamEnum, int)>
                {
                    (TileParamEnum.TileToSpawnFromReaction, tilesToSpawn),
                    (TileParamEnum.TileCreateCountForReaction, tileList.Count)
                };

                foreach (var (param, value) in tileParamList)
                {
                    SetParam(param, value);
                }

                simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.Cell.Coords,
                    tileParamList, hitContext.Hit.GetHitParams()));
            }

            if (reducedAdjacentHp <= 0)
            {
                return base.TryApplyAdjacentDamage(simulationContext, hitContext);
            }

            SetParam(TileParamEnum.AdjacentHp, reducedAdjacentHp);
            simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.MainCell.Coords,
                new List<(TileParamEnum, int)> { new(TileParamEnum.AdjacentHp, reducedAdjacentHp) },
                hitContext.Hit.GetHitParams()));
            
            return true;
        }

        private static List<TileAsset> GetWeightedRandomOutcome(GiantPinataConfiguration configuration)
        {
            var spawnCount = Math.Min(configuration.MaxSpawnCount, MaxSpawnCount);

            var selectedRewards = new List<TileAsset>(spawnCount);

            var workingRewards = configuration.AllowSameType
                ? configuration.RewardConfigurations
                : new Dictionary<GiantPinataRewardType, float>(configuration.RewardConfigurations);

            var totalWeight = 0f;
            if (configuration.AllowSameType)
            {
                foreach (var weight in workingRewards.Values)
                {
                    totalWeight += weight;
                }
            }

            for (var i = 0; i < spawnCount && workingRewards.Count > 0; i++)
            {
                if (!configuration.AllowSameType)
                {
                    totalWeight = 0f;
                    foreach (var weight in workingRewards.Values)
                    {
                        totalWeight += weight;
                    }
                }

                var randomValue = RandomSystem.Next() * totalWeight;
                var weightSum = 0f;
                var selectedReward = GiantPinataRewardType.None;

                foreach (var kvp in workingRewards)
                {
                    weightSum += kvp.Value;
                    if (randomValue <= weightSum)
                    {
                        selectedReward = kvp.Key;
                        break;
                    }
                }

                if (selectedReward == GiantPinataRewardType.None)
                {
                    selectedReward = workingRewards.Keys.DeterministicRandomInSelf();
                }

                selectedRewards.Add(ConvertGiantPinataRewardType(selectedReward));

                if (!configuration.AllowSameType)
                {
                    workingRewards.Remove(selectedReward);
                }
            }

            return selectedRewards;
        }

        private static TileAsset ConvertGiantPinataRewardType(GiantPinataRewardType giantMachineOutcome)
        {
            return giantMachineOutcome switch
            {
                GiantPinataRewardType.LineBreaker => RandomSystem.Next() > 0.5f
                    ? TileAsset.RowBreaker
                    : TileAsset.ColumnBreaker,
                GiantPinataRewardType.Bomb => TileAsset.Bomb,
                GiantPinataRewardType.ColorBomb => TileAsset.ColorBomb,
                GiantPinataRewardType.Propeller => TileAsset.Propeller,
                GiantPinataRewardType.None => TileAsset.Undefined,
                _ => throw new ArgumentOutOfRangeException(nameof(giantMachineOutcome), giantMachineOutcome,
                    $"<color=red>Unexpected Outcome in GiantPinata Mechanic '{giantMachineOutcome}'</color>")
            };
        }

        public override (Tile, TileAsset, GoalType) CreateTileFromReaction(int id, Cell cell, int index)
        {
            var tileAssetList = GetParam(TileParamEnum.TileToSpawnFromReaction);
            var tileCount = GetParam(TileParamEnum.TileCreateCountForReaction);

            var tileAssets = UnpackTileAssetsDynamic(tileAssetList, tileCount);
            var tileAsset = tileAssets[index];
            var newTile = TileFactory.CreateTile(id, tileAsset, new TileOrigin(Creator.Item, cell));

            return (newTile, tileAsset, GoalType.None);
        }

        /// <summary>
        /// Packs a list of TileAssets into an integer for dynamic tile creation.
        /// This compression is necessary because TileParam can only store integers,
        /// while the number of assets can vary. Allows storing up to 4 TileAssets
        /// (8 bits each) within a single 32-bit integer.
        /// </summary>
        private static int PackTileAssetsDynamic(List<TileAsset> assets)
        {
            var packed = 0;
            for (var i = 0; i < assets.Count; i++)
            {
                packed |= ((int)assets[i] & 0xFF) << (8 * (assets.Count - 1 - i));
            }

            return packed;
        }

        private static List<TileAsset> UnpackTileAssetsDynamic(int packed, int count)
        {
            var assets = new List<TileAsset>();
            for (var i = 0; i < count; i++)
            {
                assets.Add((TileAsset)((packed >> (8 * (count - 1 - i))) & 0xFF));
            }

            return assets;
        }
    }
}