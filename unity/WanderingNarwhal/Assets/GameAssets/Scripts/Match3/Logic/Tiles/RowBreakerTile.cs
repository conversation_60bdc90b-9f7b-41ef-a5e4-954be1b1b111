using System.Collections.Generic;
using BBB;
using BBB.Match3;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class RowBreakerTile : Tile
    {
        private const string HorizontalLb = "HorizontalLb";
        
        public RowBreakerTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Kind = TileKinds.None;
            Speciality = TileSpeciality.RowBreaker;
            BoostersApplicability = BoosterItem.All & ~BoosterItem.CreateBomb;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.TapOrSwap;
            State |= TileState.LineBreaker;
            GeneralizedLayer = HorizontalLb;
            AddMandatoryParamsTile();
        }
    }
}