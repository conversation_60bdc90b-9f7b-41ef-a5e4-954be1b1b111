using System.Collections.Generic;
using BBB;
using BBB.Match3;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class ColumnBreakerTile : Tile
    {
        private const string VerticalLb = "VerticalLb";
        
        public ColumnBreakerTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Kind = TileKinds.None;
            Speciality = TileSpeciality.ColumnBreaker;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.TapOrSwap;
            BoostersApplicability = BoosterItem.All & ~BoosterItem.CreateBomb;
            State |= TileState.LineBreaker;
            GeneralizedLayer = VerticalLb;
            AddMandatoryParamsTile();
        }
    }
}