using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using GameAssets.Scripts.Match3.Logic.Tiles;
using UnityEngine;

namespace BBB
{
    /// <summary>
    /// Grid container for m3 level cells.
    /// </summary>
    public sealed partial class Grid : IEquatable<Grid>
    {
        public Action<Cell> OnHitToCell;

        private static readonly List<Cell> MatchCellsResultTemp = new();
        private static readonly List<Cell> InvolvedCellsTemp = new();
        private static readonly HashSet<Coords> InvolvedCoordsTemp = new();
        
        private static readonly byte[] _cellIndexes = new byte[12 * 12];
        public const int MaxDimensionX = 11;
        public const int MaxDimensionY = 11;

        public static float MaxDistance => Mathf.Sqrt(MaxDimensionX * MaxDimensionX + MaxDimensionY*MaxDimensionY);

        public int Width;
        public int Height;
        private Cell[,] _coordToCells;
        public List<Cell> Cells;

        /// <summary>
        /// Used for debug logs.
        /// </summary>
        public string DebugCurrentLevelUid;

        public SandHandler SandHandler { get; } = new SandHandler();
        
        private readonly Dictionary<SlotMachineRewardType, int> _slotMachineRewardHolder = new();

        public IEnumerable<Cell> GetCellsInRandomOrder()
        {
            for (byte i = 0; i < Cells.Count; ++i)
                _cellIndexes[i] = i;
            
            _cellIndexes.DeterministicShuffle(0, Cells.Count);
            
            for (var i = 0; i < Cells.Count; ++i)
                yield return Cells[_cellIndexes[i]];
        }

        public void ForEachCellWithBreak(Func<Cell, bool> action)
        {
            var count = Cells.Count;
            for (var i = 0; i < count; i++)
            {
                if (action(Cells[i]))
                {
                    break;
                }
            }
        }
        
        public IEnumerable<Cell> GetAdjacentCells(Coords coords)
        {
            var workingCoords = new Coords(1, 0);
            for (var i = 0; i < 4; i++)
            {
                if (TryGetCell(coords + workingCoords, out var cell))
                    yield return cell;

                workingCoords = workingCoords.Perp();
            }
        }

        public List<TileKinds> GetTopTileKinds(bool checkIfColorBombTargetable = true)
        {
            var kindsNumber = Enum.GetNames(typeof(TileKinds)).Length;
            Span<(TileKinds kind, int count)> kindsCounter = stackalloc (TileKinds kind, int count)[kindsNumber];

            foreach (var workingCell in Cells)
            {
                if (workingCell.Tile.IsNull() || !workingCell.Tile.IsSimple())
                    continue;

                if (checkIfColorBombTargetable && !workingCell.IsColorBombTargetable())
                    continue;

                var kindFromTile = workingCell.Tile.Kind;
                for (var i = 0; i < kindsCounter.Length; i++)
                {
                    if (kindsCounter[i].kind == TileKinds.None)
                    {
                        kindsCounter[i] = (kindFromTile, 1);
                        break;
                    }

                    if (kindsCounter[i].kind == kindFromTile)
                    {
                        kindsCounter[i].count++;
                        break;
                    }
                }
            }

            var sortedKinds = new List<(TileKinds kind, int count)>(kindsCounter.ToArray());
            sortedKinds.Sort((a, b) => b.count.CompareTo(a.count));

            var topTileKinds = new List<TileKinds>(sortedKinds.Count);
            foreach (var keyPair in sortedKinds)
            {
                if (keyPair.kind != TileKinds.None)
                {
                    topTileKinds.Add(keyPair.kind);
                }
            }

            return topTileKinds;
        }

        public int GetSlotMachineRewardCount(SlotMachineRewardType rewardType)
        {
            return _slotMachineRewardHolder.TryGetValue(rewardType, out var timesInLevel) ? timesInLevel : 0;
        }

        public void UpdateSlotMachineRewardHolder(SlotMachineRewardType rewardType)
        {
            _slotMachineRewardHolder[rewardType] = _slotMachineRewardHolder.GetValueOrDefault(rewardType) + 1;
        }

        public Cell FindCellByTileId(int tileId)
        {
            foreach(var cell in Cells)
                if (cell.HasTile() && cell.Tile.Id == tileId)
                {
                    return cell;
                }

            return null;
        }

        public IEnumerable<Tile> GetTilesAround(Coords coords)
        {
            for(var y = coords.Y-1; y <= coords.Y+1; y++)
            for (var x = coords.X - 1; x <= coords.X + 1; x++)
            {
                if (TryGetCell(new Coords(x, y), out var cell))
                {
                    if (!cell.Tile.IsNull())
                    {
                        yield return cell.Tile;
                    }
                }
            }
        }

        public void ForEachCell(Action<Cell> action)
        {
            var count = Cells.Count;
            for (var i = 0; i < count; i++)
            {
                action(Cells[i]);
            }
        }

        public void ForEachCellReverse(Action<Cell> action)
        {
            for (var i = Cells.Count - 1; i >= 0; i--)
            {
                action(Cells[i]);
            }
        }

        private Comparison<Cell> _comparer = (a, b) =>
        {
            var vertical = a.Coords.Y.CompareTo(b.Coords.Y);
            if (vertical == 0)
            {
                var horizontal = a.Coords.X.CompareTo(b.Coords.X);
                return horizontal;
            }

            return vertical;
        };

        public Vector2 Size { get; }

        public int TilesSpawnedCount { get; set; }


        public Grid(int width, int height, int tilesSpawnedCount = 0)
        {
            Width = Mathf.Min(width, MaxDimensionX);
            Height = Mathf.Min(height, MaxDimensionY);
            Size = new Vector2(Width, Height);
            _coordToCells = new Cell[MaxDimensionX, MaxDimensionY];
            Cells = new List<Cell>();
            _slotMachineRewardHolder.Clear();

            TilesSpawnedCount = tilesSpawnedCount;
        }

        /// <summary>
        /// Backward compatibility check for existence of drop items on grid.
        /// </summary>
        /// <remarks>
        /// Used to check if drop item goal should be added at start of game.
        /// All goals must be specified explicitly, so this check should be removed later.
        /// </remarks>
        public bool HasDropItems()
        {
            foreach (var cell in Cells)
            {
                if (cell.IsAnyOf(CellState.Despawner) || (!cell.Tile.IsNull() && cell.Tile.Speciality == TileSpeciality.DropItem))
                {
                    return true;
                }
            }

            return false;
        }

        public List<Cell> GetPrioritizedMatchingCells(Func<Cell, bool> predicate)
        {
            MatchCellsResultTemp.Clear();
            InvolvedCellsTemp.Clear();
            InvolvedCoordsTemp.Clear();

            var possibleMoves = SearchMatchesSystem.SearchForAllPossibleMoves(this);
            foreach (var move in possibleMoves)
            {
                if (move.Matches == null)
                {
                    continue;
                }
                
                foreach (var coord in move.GetAllInvolvedCoords)
                {
                    InvolvedCoordsTemp.Add(coord);
                }
            }

            foreach (var cell in Cells)
            {
                if (!predicate(cell))
                {
                    continue;
                }

                if (InvolvedCoordsTemp.Contains(cell.Coords))
                {
                    InvolvedCellsTemp.Add(cell);
                }
                else
                {
                    MatchCellsResultTemp.Add(cell);
                }
            }

            //Preserving the order, so shuffle before adding to result list.
            //Final list should be ordered as:
            // 1. Cells that are not involved in any match
            // 2. Cells that are involved in matches
            MatchCellsResultTemp.DeterministicShuffle();
            InvolvedCellsTemp.DeterministicShuffle();

            MatchCellsResultTemp.AddRange(InvolvedCellsTemp);

            return MatchCellsResultTemp;
        }

        public void SortCells()
        {
            Cells.Sort(_comparer);
        }

        public Cell CreateCell(Coords coords)
        {
            var cell = new Cell(coords);
            _coordToCells[coords.X, coords.Y] = cell;
            Cells.Add(cell);
            return cell;
        }

        public void AddCell(Cell cell)
        {
            var coords = cell.Coords;
            _coordToCells[coords.X, coords.Y] = cell;
            Cells.Add(cell);
        }

        public void RemoveCell(Cell cell)
        {
            var coords = cell.Coords;
            _coordToCells[coords.X, coords.Y] = null;
            Cells.Remove(cell);
        }

        public Cell GetCell(Coords coords)
        {
            Cell cell = null;
            TryGetCell(coords, out cell);
            return cell;
        }

        public bool GetTileCoords(Tile tile, out Coords coords)
        {

            Cell cell = null;
            for (var i = Cells.Count - 1; i >= 0; i--)
            {
                if (Cells[i].Tile == tile)
                {
                    cell = Cells[i];
                    break;
                }
            }

            if (cell == null)
            {
                coords = Coords.OutOfGrid;
                return false;
            }

            coords = cell.Coords;
            return true;
        }

        public bool GetGridActor<T>(Coords coords, out T actor) where T : IGridActor
        {
            if (!TryGetCell(coords, out var cell))
            {
                actor = default(T);
                return false;
            }

            actor = (T) (typeof (T) == typeof (Cell) ? (IGridActor) cell : cell.Tile);
            return true;
        }

        public bool Contains(Coords coords)
        {
            if (coords.X < 0 || coords.X >= _coordToCells.GetLength(0) || coords.Y < 0 || coords.Y >= _coordToCells.GetLength(1))
                return false;

            return _coordToCells[coords.X, coords.Y] != null;
        }

        public bool TryGetCell(Coords coords, out Cell cell)
        {
            #if M3_PROFILE
            UnityEngine.Profiling.Profiler.BeginSample("TryGetCell");
            #endif
            cell = null;
            if (coords.X >= 0 && coords.X < Width && coords.Y >= 0 && coords.Y < Height)
            {
                cell = _coordToCells[coords.X, coords.Y];
            }
            #if M3_PROFILE
            UnityEngine.Profiling.Profiler.EndSample();
            #endif
            return cell != null;
        }

        /// <summary>
        /// Refresh runtime references in each cell if it has overlapping multi-size tile.
        /// </summary>
        public void RefrehsAllCellsMultisizeCaches()
        {
            foreach (var c in Cells)
            {
                c.ClearMultiSizeCellsReferences();
            }

            foreach (var c in Cells)
            {
                if (!c.Tile.IsNull() && c.Tile.HasParam(TileParamEnum.SizeX))
                {
                    var sizeX = c.Tile.GetParam(TileParamEnum.SizeX);
                    var sizeY = c.Tile.GetParam(TileParamEnum.SizeY);
#if UNITY_EDITOR
                    if (sizeX <= 0 || sizeY <= 0)
                    {
                        Debug.LogError($"Zero size tile detected in cell ({c.Coords.X};{c.Coords.Y})");
                        continue;
                    }

                    if (sizeX >= 12 || sizeY >= 12)
                    {
                        Debug.LogError($"Too large size tile detected in cell ({c.Coords.X};{c.Coords.Y})");
                        continue;
                    }
#endif
                }
                
                if (c.IsAnyOf(CellState.Tnt))
                {
#if UNITY_EDITOR
                    if (c.SizeX <= 0 || c.SizeY <= 0)
                    {
                        Debug.LogError($"Zero size cellstate detected in cell ({c.Coords.X};{c.Coords.Y})");
                        continue;
                    }
#endif
                    var sizeX = Mathf.Max(1, c.SizeX);
                    var sizeY = Mathf.Max(1, c.SizeY);

                    for (var x = 0; x < sizeX; x++)
                    {
                        for (var y = 0; y < sizeY; y++)
                        {
                            var coord = new Coords(c.Coords.X + x, c.Coords.Y + y);
                            if (TryGetCell(coord, out var affectedCell))
                            {
                                affectedCell.AddMultisizeCellReference(c);
                            }
                        }
                    }
                }

                if (!c.Tile.IsNull() && c.Tile.Speciality == TileSpeciality.IceBar)
                {
                    RefreshIceBar.RefreshIceBarOnGrid(this, c);
                    continue;
                }
                
                if (!c.Tile.IsNull() && c.Tile.Speciality == TileSpeciality.MetalBar)
                {
                    RefreshMetalBar.RefreshMetalBarOnGrid(this, c);
                    continue;
                }
                
                if (!c.Tile.IsNull() && c.Tile.Speciality == TileSpeciality.Shelf)
                {
                    RefreshShelf.RefreshShelfOnGrid(this, c);
                    continue;
                }
                
                if (!c.Tile.IsNull() && c.Tile.Speciality == TileSpeciality.Gondola)
                {
                    RefreshGondola.RefreshGondolaOnGrid(this, c);
                    continue;
                }
                
                if (!c.Tile.IsNull() && c.Tile.Speciality == TileSpeciality.TukTuk)
                {
                    RefreshTukTuk.RefreshTukTukOnGrid(this, c);
                    continue;
                }
                
                if (!c.Tile.IsNull() && c.Tile.HasParam(TileParamEnum.SizeX))
                {
                    var sizeX = c.Tile.GetParam(TileParamEnum.SizeX);
                    var sizeY = c.Tile.GetParam(TileParamEnum.SizeY);
                    
                    for (var x = c.Coords.X; x < c.Coords.X + sizeX; x++)
                    {
                        for (var y = c.Coords.Y; y < c.Coords.Y + sizeY; y++)
                        {
                            if (TryGetCell(new Coords(x, y), out var affectedCell))
                            {
                                affectedCell.AddMultisizeCellReference(c);
                                c.AddReferencedCell(affectedCell);
                            }
                        }
                    }
                }
            }
        }

        public bool AddWallBetween(Coords coords, CardinalDirections direction)
        {
            if (!TryGetCell(coords, out var cell)) return false;

            var newCoords = coords.GoSingleCardinalDirection(cardinalDirections: direction);
            if (!TryGetCell(newCoords, out var newCell)) return false;

            cell.AddWall(direction);
            newCell.AddWall(direction.Reversed());
            return true;
        }

        public bool AddInvisibleWallBetween(Coords coords, CardinalDirections direction)
        {
            if (!TryGetCell(coords, out var cell)) return false;
            cell.AddInvisibleWall(direction);

            var newCoords = coords.GoSingleCardinalDirection(cardinalDirections: direction);
            TryGetCell(newCoords, out var newCell);
            newCell?.AddInvisibleWall(direction.Reversed());

            return true;
        }

        public bool RemoveWallBetween(Coords coords, CardinalDirections direction)
        {
            if (!TryGetCell(coords, out var cell)) return false;

            var newCoords = coords.GoSingleCardinalDirection(cardinalDirections: direction);
            if (!TryGetCell(newCoords, out var newCell)) return false;

            cell.RemoveWall(direction);
            newCell.RemoveWall(direction.Reversed());
            return true;
        }

        public bool RemoveInvisibleWallBetween(Coords coords, CardinalDirections direction)
        {
            if (!TryGetCell(coords, out var cell)) return false;
            cell.RemoveInvisibleWall(direction);

            var newCoords = coords.GoSingleCardinalDirection(cardinalDirections: direction);
            TryGetCell(newCoords, out var newCell);
            newCell?.RemoveInvisibleWall(direction.Reversed());
            return true;
        }

        public bool HasWallBetween(Coords firstCoord, Coords secondCoord)
        {
            if (TryGetCell(firstCoord, out var firstCell) && firstCell.HasWallTo(secondCoord)) return true;
            return TryGetCell(secondCoord, out var secondCell) && secondCell.HasWallTo(firstCoord);
        }

        //use generated Grid as read only
        public Grid DefinitionAttemptClone()
        {
            var newGrid = new Grid(Width, Height, TilesSpawnedCount);
            foreach (var cell in Cells)
            {
                newGrid._coordToCells[cell.Coords.X, cell.Coords.Y] = cell;
                newGrid.Cells.Add(cell);
            }

            for (var i = 0; i < newGrid.Cells.Count; i++)
            {
                var cell = newGrid.Cells[i];
                if (cell.Tile is { Kind: TileKinds.Undefined })
                {
                    var cellClone = cell.Clone();
                    newGrid.Cells[i] = cellClone;
                    newGrid._coordToCells[cell.Coords.X, cell.Coords.Y] = cellClone;
                }
            }

            newGrid.DebugCurrentLevelUid = DebugCurrentLevelUid;
            newGrid._slotMachineRewardHolder.AddRange(_slotMachineRewardHolder);

            return newGrid;
        }

        public Grid Clone()
        {
#if M3_PROFILE
            UnityEngine.Profiling.Profiler.BeginSample("Grid cloning");
#endif
            var newGrid = new Grid(Width, Height, TilesSpawnedCount);

            foreach (var cell in Cells)
            {
                var newCell = cell.Clone();
                newGrid._coordToCells[newCell.Coords.X, newCell.Coords.Y] = newCell;
                newGrid.Cells.Add(newCell);
            }

            newGrid.DebugCurrentLevelUid = DebugCurrentLevelUid;
            newGrid._slotMachineRewardHolder.AddRange(_slotMachineRewardHolder);
#if M3_PROFILE
            UnityEngine.Profiling.Profiler.EndSample();
#endif

            return newGrid;
        }


        public Grid CloneIntoUndefinedShuffleable()
        {
            var newGrid = new Grid(Width, Height, TilesSpawnedCount);
            var newCoordAndCell = newGrid._coordToCells;
            var newCells = newGrid.Cells;

            foreach (var cell in Cells)
            {
                Cell newCell;
                if (cell.IsShuffable())
                    newCell = cell.CloneIntoUndefined();
                else
                    newCell = cell.Clone();

                newCoordAndCell[cell.Coords.X, cell.Coords.Y] = newCell;
                newCells.Add(newCell);
            }

            newGrid.DebugCurrentLevelUid = DebugCurrentLevelUid;

            newGrid.RefrehsAllCellsMultisizeCaches();
            return newGrid;
        }

        public Vector2 GetCenterVec2()
        {
            var first = Cells[0].Coords.ToUnityVector2();
            var last = Cells[^1].Coords.ToUnityVector2();
            return (first + last) / 2f;
        }

        public bool IsSameKind(Coords firstCoords, Coords secondCoords, out TileKinds kind)
        {
            if (TryGetCell(firstCoords, out var firstCell) && TryGetCell(secondCoords, out var secondCell))
            {
                if (ReferenceEquals(firstCell.Tile, null) || ReferenceEquals(secondCell.Tile, null))
                {
                    kind = TileKinds.None;
                    return false;
                }

                if (firstCell.Tile.Kind.Equals(secondCell.Tile.Kind))
                {
                    kind = firstCell.Tile.Kind;
                    return true;
                }

                kind = TileKinds.None;
                return false;
            }

            kind = TileKinds.None;
            return false;
        }

        public int CountAssistMarkers(IEnumerable<Match> matches)
        {
            var result = 0;
            foreach (var match in matches)
            {
                foreach (var coord in match.GetAllCoords())
                {
                    if (TryGetCell(coord, out var cell))
                        if (cell.IsAnyOf(CellState.AssistMarker))
                            result++;
                }
            }

            return result;
        }

        public int CountAllTiles(Predicate<Tile> predicate)
        {
            var count = 0;
            foreach(var cell in Cells)
                if (!ReferenceEquals(cell.Tile, null) && predicate(cell.Tile))
                    count++;

            return count;
        }

        public bool IsAnyTile(Predicate<Tile> predicate)
        {
            foreach(var cell in Cells)
                if (!ReferenceEquals(cell.Tile, null) && predicate(cell.Tile))
                    return true;

            return false;
        }

        public bool IsKindAt(Coords coords, TileKinds kind)
        {
            if (TryGetCell(coords, out var cell) && !ReferenceEquals(cell.Tile, null))
            {
                return cell.Tile.Kind == kind;
            }

            return false;
        }

        public bool IsOutsideGridBounds(int x, int y)
        {
            if (x < 0) return true;
            if (x >= Width) return true;
            if (y < 0) return true;
            if (y >= Height) return true;
            return false;
        }

        public bool HasStateAt(int x, int y, TileState state)
        {
            if (TryGetCell(new Coords(x, y), out var cell) && !ReferenceEquals(cell.Tile, null))
            {
                return cell.Tile.IsAnyOf(state);
            }

            return false;
        }

        public bool HasSpecAt(Coords coords, TileSpeciality spec)
        {
            if (TryGetCell(coords, out var cell) && !ReferenceEquals(cell.Tile, null))
            {
                return cell.Tile.Speciality == spec;
            }

            return false;
        }

        public void ForeachTile(Action<Tile, Coords> action)
        {
            foreach(var cell in Cells)
                if (!ReferenceEquals(cell.Tile, null))
                    action(cell.Tile, cell.Coords);
        }

        public bool DoesColumnContain(int x, Predicate<Tile> predicate)
        {
            return DoesColumnContain(x, cell => !ReferenceEquals(cell.Tile, null) && predicate(cell.Tile));
        }
        
        public bool DoesColumnContain(int x, Predicate<Cell> predicate)
        {
            for (var y = 0; y < Height; y++)
            {
                var coord = new Coords(x, y);
                if (TryGetCell(coord, out var cell) && predicate(cell))
                    return true;
            }

            return false;
        }

        public bool GetBoundingRect(TileKinds kind, out Rectangle rect)
        {
            var minX = int.MaxValue;
            var minY = int.MaxValue;
            var maxX = int.MinValue;
            var maxY = int.MaxValue;

            foreach(var cell in Cells)
                if (!ReferenceEquals(cell.Tile, null) && cell.Tile.Kind == kind)
                {
                    var coords = cell.Coords;

                    if (coords.X < minX)
                        minX = coords.X;

                    if (coords.X > maxX)
                        maxX = coords.X;

                    if (coords.Y < minY)
                        minY = coords.Y;

                    if (coords.Y > maxY)
                        maxY = coords.Y;
                }

            if (minX == int.MaxValue || minY == int.MaxValue || maxX == int.MinValue || maxY == int.MinValue)
            {
                rect = Rectangle.Empty;
                return false;
            }

            rect = Rectangle.Create(minX, maxY, maxX, minY);
            return true;
        }

        public IEnumerable<Cell> GetCellsInRow(int y)
        {
            for (var x = 0; x < Width; x++)
            {
                var coord = new Coords(x, y);
                if (TryGetCell(coord, out var cell))
                    yield return cell;
            }
        }
        
        public IEnumerable<Cell> GetCellsInColl(int x)
        {
            for (var y = 0; y < Height; y++)
            {
                var coord = new Coords(x, y);
                if (TryGetCell(coord, out var cell))
                    yield return cell;
            }
        }

        public bool DoesRowContain(int y, Predicate<Tile> predicate)
        {
            return DoesRowContain(y, cell => !ReferenceEquals(cell.Tile, null) && predicate(cell.Tile));
        }

        public bool DoesRowContain(int y, Predicate<Cell> predicate)
        {
            foreach (var cell in GetCellsInRow(y))
            {
                if (predicate(cell))
                    return true;
            }
            return false;
        }

        public IEnumerable<Cell> GetCellsInSquare(Coords coords, int side)
        {
            var offset = side / 2;
            var xMin = coords.X - offset;
            var xMax = coords.X + offset;
            var yMin = coords.Y - offset;
            var yMax = coords.Y + offset;

            for (var x = xMin; x <= xMax; x++)
            for(var y = yMin; y <= yMax; y++)
            {

                if (TryGetCell(new Coords(x, y), out var cell))
                    yield return cell;
            }
        }

        public bool DoesSquareContain(Coords coords, int side, Predicate<Cell> predicate)
        {
            foreach (var cell in GetCellsInSquare(coords, side))
            {
                if (predicate(cell))
                    return true;
            }

            return false;
        }

        public bool Equals(Grid other)
        {
            if (ReferenceEquals(null, other)) return false;
            if (ReferenceEquals(this, other)) return true;
            return Width == other.Width
                   && Height == other.Height
                   && Equals(_coordToCells, other._coordToCells)
                   && TilesSpawnedCount == other.TilesSpawnedCount;
        }

        public override int GetHashCode()
        {
            unchecked
            {
                var hashCode = Width;
                hashCode = (hashCode * 397) ^ Height;
                hashCode = (hashCode * 397) ^ (_coordToCells != null ? _coordToCells.GetHashCode() : 0);
                hashCode = (hashCode * 397) ^ TilesSpawnedCount;
                return hashCode;
            }
        }

        public bool HasAnyUndefinedTile()
        {
            foreach (var cell in Cells)
            {
                if (ReferenceEquals(cell.Tile, null))
                    continue;

                if (cell.Tile.Kind == TileKinds.Undefined)
                    return true;
            }

            return false;
        }

        public bool HasAnyDefinedTile()
        {
            foreach (var cell in Cells)
            {
                if (ReferenceEquals(cell.Tile, null))
                    continue;

                if (cell.Tile.Kind == TileKinds.Undefined)
                    continue;

                return true;
            }

            return false;
        }

        public void ReportHitToCell(Cell cell)
        {
            OnHitToCell?.Invoke(cell);
        }

        public bool IsSwappableCell(Coords coord)
        {
            if (!TryGetCell(coord, out var cell) || !cell.IsBaseCellSwappable()) return false;
            
            if (cell.Tile is null)
            {
                var acceptingTile = !cell.IsAnyOf(CellState.NotAcceptingTiles);

                if (acceptingTile)
                    return true;
            }
            else
            {
                var swappable = !cell.Tile.IsAnyOf(TileState.NotSwappable);

                if (swappable)
                    return true;
            }

            return false;
        }

        public bool IsSwapAllowedBetween(Coords first, Coords second)
        {
            if (first == second)
                return false;

            return !HasWallBetween(first, second);
        }
    }
}
