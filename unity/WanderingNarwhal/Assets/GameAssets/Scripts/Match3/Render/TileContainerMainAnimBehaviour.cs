using UnityEngine;

public class TileContainerMainAnimBehaviour : StateMachineBehaviour
{
    private float _timer = -1f;
    private static int InitialStateHash = Animator.StringToHash("InitialState");
    private static int ExecutingHash = Animator.StringToHash("Executing");

    // OnStateEnter is called when a transition starts and the state machine starts to evaluate this state
   public override void OnStateEnter(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
   {
       _timer = 5f;
   }
   
   //OnStateExit is called when a transition ends and the state machine finishes evaluating this state
   public override void OnStateExit(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
   {
       _timer = -1f;
   }

    // OnStateUpdate is called on each Update frame between OnStateEnter and OnStateExit callbacks
    public override void OnStateUpdate(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
    {
        if (_timer >= 0f)
        {
            _timer -= Time.deltaTime;
            if (_timer < 0f)
            {
                if (stateInfo.shortNameHash == InitialStateHash && !animator.GetBool(ExecutingHash))
                {
                    animator.enabled = false;
                    _timer = -1f;
                }
                else
                    _timer = 5f;
            }
        }
    }

    // OnStateMove is called right after Animator.OnAnimatorMove()
    //override public void OnStateMove(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
    //{
    //    // Implement code that processes and affects root motion
    //}

    // OnStateIK is called right after Animator.OnAnimatorIK()
    //override public void OnStateIK(Animator animator, AnimatorStateInfo stateInfo, int layerIndex)
    //{
    //    // Implement code that sets up animation IK (inverse kinematics)
    //}
}
