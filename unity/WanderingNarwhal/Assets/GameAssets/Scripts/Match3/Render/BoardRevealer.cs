using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core;
using BBB.DI;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public interface IBoardRevealObserver
    {
        void OnBoardRevealStart();
    }

    public class BoardRevealStarted : IEvent
    {

    }
    
    public class BoardRevealer
    {
        private const float BoardRevealTime = 0.5f;
        
        private readonly IContext _context;
        private readonly GameController _gameController;
        private readonly List<IRevealableBoardElement> _revealables = new();

        private IEventDispatcher _eventDispatcher;
        
        private Comparison<IRevealableBoardElement> _sorting = (a, b) => a.GetPosition().x.CompareTo(b.GetPosition().x);

        public BoardRevealer(IContext context)
        {
            _context = context;
            _gameController = context.Resolve<GameController>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
        }
        
        public void Refresh()
        {
            _revealables.Clear();
            FetchRevealables<IPerimeterRenderer>(_context);
            FetchRevealables<ICellController>(_context);
            _revealables.Sort(_sorting);
        }

        private void FetchRevealables<T>(IContext context) where T : class
        {
            var obj = context.Resolve<T>();

            if (obj is not IRevealablesContainer container)
                throw new InvalidCastException(string.Format("{0} is not implementing IRevealablesContainer", obj.GetType().Name));

            container.InsertRevealablesInto(_revealables);
        }

        public void Reveal(Action onDone)
        {
            if (_revealables.Count == 0)
            {
                onDone.SafeInvoke();
                return;
            }
            
            _gameController.StartCoroutineMethod(RevealRoutine(onDone));
        }

        private IEnumerator RevealRoutine(Action onDone)
        {
            _eventDispatcher.TriggerEventNextFrame(_eventDispatcher.GetMessage<BoardRevealStarted>());
            AudioProxy.PlaySound(Match3SoundIds.BoardRevealStart);
            
            BDebug.Log(LogCat.Match3, $"Board Reveal Start, InputLock = true");
            _gameController.LockInput(true);

            foreach (var revealable in _revealables)
            {
                revealable.Hide();
                revealable.Reveal(BoardRevealTime);
            }

            yield return WaitCache.Seconds(BoardRevealTime);
            
            BDebug.Log(LogCat.Match3, $"Board Reveal done, InputLock = false");
            
            _gameController.LockInput(false);
            
            onDone.SafeInvoke();
        }
    }
}