using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Match3.Logic.Tiles;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SlotMachineLayerView : SheetTileLayerViewBase
    {
        private SlotMachineLayerRenderer _renderer;

        private int _prevLevel = -1;
        private int _size;
        
        public SlotMachineLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<SlotMachineLayerRenderer>();
            _renderer.CellSize = cellSize;
            _renderer.InitialSetup(M3Settings);
        }
        
        protected override int SelectSheetsCountValue(Tile tile)
        {
            var count = tile.GetParam(TileParamEnum.AdjacentHp);
            return count;
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            if (newLevel < _prevLevel)
            {
                if (_renderer.FinalRewardType == SlotMachineRewardType.None)
                {
                    var currentOutcome = (SlotMachineRewardType)tile.GetParam(TileParamEnum.SlotMachineOutcome);
                    var tileAsset = (TileAsset)tile.GetParam(TileParamEnum.TileToSpawnFromReaction);
                    _renderer.SetupOutcome(currentOutcome, tileAsset);
                }
                
                _renderer.PlayHit(_prevLevel);
                if (coords.HasValue && newLevel != 0)
                {
                    var fx = (FxType)((int)FxType.SlotMachineTileHideLayer1 - newLevel);
                    FxRenderer.SpawnSingleAnimatorEffect(coords.Value, fx, _renderer.FxDestroyDuration);
                    switch (newLevel)
                    {
                        case 3:
                            AudioProxy.PlaySound(Match3SoundIds.SlotMachineHitOne);
                            break;
                        case 2:
                            AudioProxy.PlaySound(Match3SoundIds.SlotMachineHitTwo);
                            break;
                        case 1:
                            AudioProxy.PlaySound(Match3SoundIds.SlotMachineHitThree);
                            break;
                    }
                }
            }
            _prevLevel = newLevel;
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim, TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
            base.Animate(coords, anim, animParams);
            switch (anim)
            {
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;

                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;

                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;

                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    AudioProxy.PlaySound(Match3SoundIds.SlotMachineRollWin);
                    AudioProxy.PlaySound(Match3SoundIds.SlotMachineDestroyBox);
                    _renderer.PlayDestroy(onDone: OnDone);
                    break;

                    void OnDone()
                    {
                        FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.SlotMachineTileHideLayer1, 2f);
                        IsPlayingLayerViewAnimation = false;
                    }
            }
        }
    }
}
