using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public static class TileLayerStateExtensions
    {
        private static readonly Dictionary<TileSpeciality, TileLayerState> TileSpecialityToLayerState = new()
        {
            { TileSpeciality.None, TileLayerState.Normal },
            { TileSpeciality.Sticker, TileLayerState.Sticker },
            { TileSpeciality.RowBreaker, TileLayerState.HorizontalLb },
            { TileSpeciality.ColumnBreaker, TileLayerState.VerticalLb },
            { TileSpeciality.Bomb, TileLayerState.Bomb },
            { TileSpeciality.BlinkingTile, TileLayerState.Blinking },
            { TileSpeciality.ColorBomb, TileLayerState.ColorBomb },
            { TileSpeciality.DropItem, TileLayerState.DropItem },
            { TileSpeciality.Litter, TileLayerState.Litter },
            { TileSpeciality.Sand, TileLayerState.Sand },
            { TileSpeciality.Pinata, TileLayerState.Pinata },
            { TileSpeciality.Frame, TileLayerState.Frame },
            { TileSpeciality.ColorCrate, TileLayerState.ColorCrate },
            { TileSpeciality.Watermelon, TileLayerState.Watermelon },
            { TileSpeciality.MoneyBag, TileLayerState.MoneyBag },
            { TileSpeciality.Penguin, TileLayerState.Penguin },
            { TileSpeciality.Egg, TileLayerState.Egg },
            { TileSpeciality.Bird, TileLayerState.Bird },
            { TileSpeciality.Sheep, TileLayerState.Sheep },
            { TileSpeciality.Banana, TileLayerState.Banana },
            { TileSpeciality.Monkey, TileLayerState.Monkey },
            { TileSpeciality.Skunk, TileLayerState.Skunk },
            { TileSpeciality.Hen, TileLayerState.Hen },
            { TileSpeciality.Chicken, TileLayerState.Chicken },
            { TileSpeciality.Hive, TileLayerState.Hive },
            { TileSpeciality.Bee, TileLayerState.Bee },
            { TileSpeciality.Mole, TileLayerState.Mole },
            { TileSpeciality.Squid, TileLayerState.Squid },
            { TileSpeciality.Toad, TileLayerState.Toad },
            { TileSpeciality.MagicHat, TileLayerState.MagicHat },
            { TileSpeciality.Propeller, TileLayerState.Propeller },
            { TileSpeciality.Bowling, TileLayerState.Bowling },
            { TileSpeciality.Bush, TileLayerState.Bush },
            { TileSpeciality.Soda, TileLayerState.Soda },
            { TileSpeciality.Safe, TileLayerState.Safe },
            { TileSpeciality.FlowerPot, TileLayerState.FlowerPot },
            { TileSpeciality.IceBar, TileLayerState.IceBar },
            { TileSpeciality.DynamiteBox, TileLayerState.DynamiteBox },
            { TileSpeciality.GiantPinata, TileLayerState.GiantPinata },
            { TileSpeciality.MetalBar, TileLayerState.MetalBar },
            { TileSpeciality.Shelf, TileLayerState.Shelf },
            { TileSpeciality.JellyFish, TileLayerState.JellyFish },
            { TileSpeciality.GoldenScarab, TileLayerState.GoldenScarab },
            { TileSpeciality.Gondola, TileLayerState.Gondola },
            { TileSpeciality.TukTuk, TileLayerState.TukTuk },
            { TileSpeciality.FireWorks, TileLayerState.FireWorks },
            { TileSpeciality.SlotMachine, TileLayerState.SlotMachine },
            { TileSpeciality.BigMonkey, TileLayerState.BigMonkey }
        };
        
        public static int ToSortOrder(this TileLayerState layerState)
        {
            switch (layerState)
            {
                case TileLayerState.Normal:
                case TileLayerState.Undefined:
                case TileLayerState.Blinking:
                    return 10;
                case TileLayerState.HorizontalLb:
                case TileLayerState.VerticalLb:
                case TileLayerState.MoneyBag:
                case TileLayerState.Penguin:
                case TileLayerState.Egg:
                case TileLayerState.Banana:
                case TileLayerState.Chicken:
                case TileLayerState.Bee:
                case TileLayerState.FlowerPot:
                case TileLayerState.TukTuk:    
                    return 20;
                case TileLayerState.Sticker:
                case TileLayerState.Litter:
                case TileLayerState.Pinata:
                case TileLayerState.Frame:
                case TileLayerState.ColorCrate:
                case TileLayerState.Watermelon:
                case TileLayerState.Mole:
                    return 30;
                case TileLayerState.Skunk:
                case TileLayerState.Hen:
                case TileLayerState.Hive:
                case TileLayerState.Bomb:
                case TileLayerState.Propeller:
                case TileLayerState.ColorBomb:
                case TileLayerState.Squid:
                    return 40;
                case TileLayerState.DropItem:
                    return 50;
                case TileLayerState.StealingHatLabel:
                    return 55;
                case TileLayerState.Vase:
                    return 80;
                case TileLayerState.Bird:
                    return 101;
                case TileLayerState.Sheep:
                case TileLayerState.Monkey:
                case TileLayerState.BigMonkey:
                    // Animals should be higher than Walls, and walls have 100'th order
                    // (walls order, as well as other cell overlays order, is defined in TileResourcesExtra asset). -VK
                    return 102;
                case TileLayerState.GameEventLabel:
                    return 105;
                case TileLayerState.Bowling:
                case TileLayerState.Bush:
                case TileLayerState.Soda:
                case TileLayerState.MagicHat:
                case TileLayerState.Safe:
                case TileLayerState.IceBar:
                case TileLayerState.MetalBar:
                case TileLayerState.DynamiteBox:
                case TileLayerState.GiantPinata:
                case TileLayerState.Shelf:
                case TileLayerState.JellyFish:
                case TileLayerState.GoldenScarab:
                case TileLayerState.FireWorks:
                case TileLayerState.SlotMachine:
                    return 109;
                case TileLayerState.Animal:
                    return 110;
                //Armour Mods should be rendered on top of almost all elements
                case TileLayerState.Chained:
                    return 120;
                case TileLayerState.IceCube:
                    return 121;
                case TileLayerState.Sand:
                    return 122;
                //Toad is the highest layered since the toad animation jumps on board
                //This will prevent the use of Ivy on top of the Toad.
                case TileLayerState.Toad:
                    return 123;
                //Gondola Flag is at layer 126,
                case TileLayerState.Gondola:
                    return 127;
            }

            UnityEngine.Debug.LogError($"Order value not found for {layerState}");

            return 0;
        }

        public static string ToFullAssetPath(this TileLayerState layerState)
        {
            const string prefix = "Prefabs/TileComponentPrefabs/";
            return prefix + ToPrefabName(layerState, noErrors: true);
        }

        public static TileLayerState GetTileLayerFromInt(this int val)
        {
            return (TileLayerState) ((ulong) 1 << val);
        }

        public static string ToPrefabName(this TileLayerState layerState, bool noErrors = false)
        {
            switch (layerState)
            {
                case TileLayerState.Normal:
                case TileLayerState.Undefined:
                    return "TileInstance";
                case TileLayerState.HorizontalLb:
                case TileLayerState.VerticalLb:
                    return "LineBreakerBoost";
                case TileLayerState.Bomb:
                    return "BombBoost";
                case TileLayerState.Propeller:
                    return "PropellerBoost";
                case TileLayerState.ColorBomb:
                    return "ColorBombBoost";
                case TileLayerState.Sticker:
                    return "StickerBlocker";
                case TileLayerState.ColorCrate:
                    return "ColorCrate";
                case TileLayerState.DropItem:
                    return "DropItem";
                case TileLayerState.Litter:
                    return "Litter";
                case TileLayerState.Chained:
                    return "Chain";
                case TileLayerState.Blinking:
                    return "BlinkingTile";
                case TileLayerState.IceCube:
                    return "IceCube";
                case TileLayerState.Sand:
                    return "Sand";
                case TileLayerState.Pinata:
                    return "Pinata";
                case TileLayerState.Frame:
                    return "Frame";
                case TileLayerState.Animal:
                    return "Animal";
                case TileLayerState.Watermelon:
                    return "Watermelon";
                case TileLayerState.Vase:
                    return "Vase";
                case TileLayerState.MoneyBag:
                    return "MoneyBag";
                case TileLayerState.Penguin:
                    return "Penguin";
                case TileLayerState.Egg:
                    return "Egg";
                case TileLayerState.FlowerPot:
                    return "FlowerPot";
                case TileLayerState.Bird:
                    return "BirdTile";
                case TileLayerState.Sheep:
                    return "Sheep";
                case TileLayerState.Banana:
                    return "Banana";
                case TileLayerState.Monkey:
                    return "Monkey";
                case TileLayerState.BigMonkey:
                    return "BigMonkey";
                case TileLayerState.Skunk:
                    return "Skunk";
                case TileLayerState.GameEventLabel:
                    return "GameEventLabel";
                case TileLayerState.Hen:
                    return "HenTile";
                case TileLayerState.Chicken:
                    return "ChickenTile";
                case TileLayerState.Hive:
                    return "HiveTile";
                case TileLayerState.Bee:
                    return "BeeTile";
                case TileLayerState.Mole:
                    return "MoleTile";
                case TileLayerState.Squid:
                    return "SquidTile";
                case TileLayerState.StealingHatLabel:
                    return "DidiHat";
                case TileLayerState.Toad:
                    return "ToadTile";
                case TileLayerState.MagicHat:
                    return "MagicHatTile";
                case TileLayerState.Bowling:
                    return "BowlingTile";
                case TileLayerState.Bush:
                    return "BushTile";
                case TileLayerState.Soda:
                    return "SodaTile";
                case TileLayerState.Safe:
                    return "SafeTile";
                case TileLayerState.IceBar:
                    return "IceBarTile";
                case TileLayerState.DynamiteBox:
                    return "DynamiteBoxTile";
                case TileLayerState.GiantPinata:
                    return "GiantPinataTile";
                case TileLayerState.MetalBar:
                    return "MetalBarTile";
                case TileLayerState.Shelf:
                    return "ShelfTile";
                case TileLayerState.JellyFish:
                    return "JellyFishTile";
                case TileLayerState.GoldenScarab:
                    return "GoldenScarabTile";
                case TileLayerState.Gondola:
                    return "GondolaTile";
                case TileLayerState.TukTuk:
                    return "TukTukTile";
                case TileLayerState.FireWorks:
                    return "FireWorksTile";
                case TileLayerState.SlotMachine:
                    return "SlotMachineTile";
            }

            if (noErrors)
            {
                return "";
            }

            UnityEngine.Debug.LogError($"Prefab name not found for {layerState}");

            return "DummyTile";
        }

        public static IEnumerable<FxType> ToFxTypes(this TileLayerState tileLayerState)
        {
            if ((tileLayerState & TileLayerState.ColorBomb) != 0)
                yield return FxType.LightningBolt;

            if ((tileLayerState & TileLayerState.Sticker) != 0)
            {
                yield return FxType.StickerRemove;
                yield return FxType.StickerSecondLayerRemove;
                yield return FxType.StickerThirdLayerRemove;
            }

            if ((tileLayerState & TileLayerState.ColorCrate) != 0)
            {
                yield return FxType.ColorCrateDestroy;
                yield return FxType.ColorCrateSecondLayerRemove;
                yield return FxType.ColorCrateThirdLayerRemove;
            }

            if ((tileLayerState & TileLayerState.Chained) != 0)
                yield return FxType.ChainRemove;

            if ((tileLayerState & TileLayerState.IceCube) != 0)
                yield return FxType.IceCubeRemoval;

            if ((tileLayerState & TileLayerState.Pinata) != 0)
                yield return FxType.PinataRemoval;

            if ((tileLayerState & TileLayerState.Animal) != 0)
                yield return FxType.AnimalRelease;

            if ((tileLayerState & TileLayerState.Frame) != 0)
            {
                yield return FxType.FrameRemoval;
                yield return FxType.FrameLayerRemoval;
            }

            if ((tileLayerState & TileLayerState.Sand) != 0)
                yield return FxType.SandRemove;

            if ((tileLayerState & TileLayerState.Watermelon) != 0)
                yield return FxType.WatermelonDestroy;

            if ((tileLayerState & TileLayerState.Vase) != 0)
                yield return FxType.VaseDestroy;

            if ((tileLayerState & TileLayerState.MoneyBag) != 0)
            {
                yield return FxType.MoneyBagDestroy;
                yield return FxType.MoneyBagGoal;
            }

            if ((tileLayerState & TileLayerState.Penguin) != 0)
                yield return FxType.PenguinDestroy;

            if ((tileLayerState & TileLayerState.Egg) != 0)
            {
                yield return FxType.EggDestroy;
                yield return FxType.EggLayerRemove;
                yield return FxType.BirdAppear;
                yield return FxType.BirdDestroy;
            }
            if ((tileLayerState & TileLayerState.FlowerPot) != 0)
            {
                yield return FxType.FlowerPotLayerRemove;
                yield return FxType.FlowerPotDestroy;
            }

            if ((tileLayerState & TileLayerState.Bird) != 0)
            {
                yield return FxType.BirdAppear;
                yield return FxType.BirdDestroy;
            }

            if ((tileLayerState & TileLayerState.Sheep) != 0)
            {
                yield return FxType.SheepDestroy;
            }

            if ((tileLayerState & (TileLayerState.Banana | TileLayerState.Monkey | TileLayerState.BigMonkey)) != 0)
            {
                yield return FxType.BananaAppear;
                yield return FxType.BananaDestroy;
            }

            if ((tileLayerState & TileLayerState.Skunk) != 0)
            {
                yield return FxType.SkunkAttack;
            }

            if ((tileLayerState & (TileLayerState.Hen | TileLayerState.Chicken)) != 0)
            {
                yield return FxType.ChickenAppear;
                yield return FxType.ChickenDestroy;
            }

            if ((tileLayerState & (TileLayerState.Hive | TileLayerState.Bee)) != 0)
            {
                yield return FxType.BeeAppear;
                yield return FxType.BeeDestroy;
            }

            if ((tileLayerState & TileLayerState.Mole) != 0)
            {
                yield return FxType.MoleDestroy;
            }

            if ((tileLayerState & TileLayerState.Squid) != 0)
            {
                yield return FxType.SquidDestroy;
                yield return FxType.SquidGoal;
                yield return FxType.SquidHit;
            }

            if ((tileLayerState & TileLayerState.Toad) != 0)
            {
                yield return FxType.ToadGoal;
            }
            
            if ((tileLayerState & TileLayerState.MagicHat) != 0)
            {
                yield return FxType.MagicHatGoal;
            }

            if ((tileLayerState & TileLayerState.Bush) != 0)
            {
                yield return FxType.GrassAnticipation;
            }

            if ((tileLayerState & TileLayerState.Litter) != 0)
            {
                yield return FxType.LitterDestroy;
            }
            
            if ((tileLayerState & TileLayerState.TukTuk) != 0)
            {
                yield return FxType.TukTukEffect;
            }
            
            if ((tileLayerState & TileLayerState.FireWorks) != 0)
            {
                yield return FxType.FireWorksFlight;
                yield return FxType.FireWorksDestroy;
                yield return FxType.FireWorksRemoveLayer;
                yield return FxType.FireWorksTileDestroy;
            }
        }

        public static TileLayerState ToLayerState(this Tile tile)
        {
            var result = TileLayerState.None;

            if (ReferenceEquals(tile, null))
                return result;

            result |= tile.TileToLayerState();

            return result;
        }

        private static TileLayerState TileToLayerState(this Tile tile)
        {
            return tile.Speciality.ToLayerState() | tile.State.ToLayerState();
        }

        public static TileLayerState ToLayerState(this TileSpeciality tileSpeciality)
        {
            if (TileSpecialityToLayerState.TryGetValue(tileSpeciality, out var layerState))
            {
                return layerState;
            }

            throw new ArgumentOutOfRangeException(nameof(tileSpeciality), tileSpeciality,
                "TileSpeciality not found in the dictionary.");
        }

        public static TileLayerState ToLayerState(this TileState state)
        {
            return ((state & TileState.ChainMod) != 0 ? TileLayerState.Chained : TileLayerState.None)
                   | ((state & TileState.IceCubeMod) != 0 ? TileLayerState.IceCube : TileLayerState.None)
                   | ((state & TileState.ChainMod) != 0 ? TileLayerState.Chained : TileLayerState.None)
                   | ((state & TileState.AnimalMod) != 0 ? TileLayerState.Animal : TileLayerState.None)
                   | ((state & TileState.SandMod) != 0 ? TileLayerState.Sand : TileLayerState.None)
                   | ((state & TileState.ColorCrateMod) != 0 ? TileLayerState.ColorCrate : TileLayerState.None)
                   | ((state & TileState.VaseMod) != 0 ? TileLayerState.Vase : TileLayerState.None)
                   | ((state & TileState.MoneyBagMod) != 0 ? TileLayerState.MoneyBag : TileLayerState.None)
                   | ((state & TileState.PenguinMod) != 0 ? TileLayerState.Penguin : TileLayerState.None)
                   | ((state & TileState.EggMod) != 0 ? TileLayerState.Egg : TileLayerState.None)
                   | ((state & TileState.BirdMod) != 0 ? TileLayerState.Bird : TileLayerState.None)
                   | ((state & TileState.SheepMod) != 0 ? TileLayerState.Sheep : TileLayerState.None)
                   | ((state & TileState.BananaMod) != 0 ? TileLayerState.Banana : TileLayerState.None)
                   | ((state & TileState.MonkeyMod) != 0 ? TileLayerState.Monkey : TileLayerState.None)
                   | ((state & TileState.BigMonkeyMod) != 0 ? TileLayerState.BigMonkey : TileLayerState.None)
                   | ((state & TileState.SkunkMod) != 0 ? TileLayerState.Skunk : TileLayerState.None)
                   | ((state & TileState.GameEventLabel) != 0 ? TileLayerState.GameEventLabel : TileLayerState.None)
                   | ((state & TileState.HenMod) != 0 ? TileLayerState.Hen : TileLayerState.None)
                   | ((state & TileState.ChickenMod) != 0 ? TileLayerState.Chicken : TileLayerState.None)
                   | ((state & TileState.HiveMod) != 0 ? TileLayerState.Hive : TileLayerState.None)
                   | ((state & TileState.BeeMod) != 0 ? TileLayerState.Bee : TileLayerState.None)
                   | ((state & TileState.MoleMod) != 0 ? TileLayerState.Mole : TileLayerState.None)
                   | ((state & TileState.SquidMod) != 0 ? TileLayerState.Squid : TileLayerState.None)
                   | ((state & TileState.StealingHatLabel) != 0 ? TileLayerState.StealingHatLabel : TileLayerState.None)
                   | ((state & TileState.ToadMod) != 0 ? TileLayerState.Toad : TileLayerState.None)
                   | ((state & TileState.BowlingMod) != 0 ? TileLayerState.Bowling : TileLayerState.None)
                   | ((state & TileState.BushMod) != 0 ? TileLayerState.Bush : TileLayerState.None)
                   | ((state & TileState.IceBarMod) != 0 ? TileLayerState.IceBar : TileLayerState.None)
                   | ((state & TileState.SodaMod) != 0 ? TileLayerState.Soda : TileLayerState.None)
                   | ((state & TileState.MagicHatMod) != 0 ? TileLayerState.MagicHat : TileLayerState.None)
                   | ((state & TileState.SafeMod) != 0 ? TileLayerState.Safe : TileLayerState.None)
                   | ((state & TileState.FlowerPotMod) != 0 ? TileLayerState.FlowerPot : TileLayerState.None)
                   | ((state & TileState.DynamiteBoxMod) != 0 ? TileLayerState.DynamiteBox : TileLayerState.None)
                   | ((state & TileState.GiantPinataMod) != 0 ? TileLayerState.GiantPinata : TileLayerState.None)
                   | ((state & TileState.MetalBarMod) != 0 ? TileLayerState.MetalBar : TileLayerState.None)
                   | ((state & TileState.ShelfMod) != 0 ? TileLayerState.Shelf : TileLayerState.None)
                   | ((state & TileState.JellyFishMod) != 0 ? TileLayerState.JellyFish : TileLayerState.None)
                   | ((state & TileState.GoldenScarabMod) != 0 ? TileLayerState.GoldenScarab : TileLayerState.None)
                   | ((state & TileState.GondolaMod) != 0 ? TileLayerState.Gondola : TileLayerState.None)
                   | ((state & TileState.TukTukMod) != 0 ? TileLayerState.TukTuk : TileLayerState.None)
                   | ((state & TileState.FireWorksMod) != 0 ? TileLayerState.FireWorks : TileLayerState.None)
                   | ((state & TileState.SlotMachineMod) != 0 ? TileLayerState.SlotMachine : TileLayerState.None);
        }
    }

    [AttributeUsage(AttributeTargets.Field)]
    public class TileLayerStateNameAttribute : PropertyAttribute
    {
    }

#if UNITY_EDITOR

    [CustomPropertyDrawer(typeof(TileLayerStateNameAttribute))]
    public class TileMechanicHelpInfoDrawer : PropertyDrawer
    {
        private static readonly GUIStyle LabelStyle = GUI.skin.GetStyle("miniLabel");

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return EditorGUI.GetPropertyHeight(property, label, includeChildren: true);
        }

        public override void OnGUI(Rect position, SerializedProperty property,
            GUIContent label)
        {
            EditorGUI.PropertyField(position, property, label);
            var layerStateValue = property.intValue.GetTileLayerFromInt();
            const int width = 120;
            var labelPosition =
                new Rect(new Vector2(position.xMax - width - 10f, position.yMin),
                    new Vector2(width, 20f));
            EditorGUI.LabelField(labelPosition, layerStateValue.ToString(), LabelStyle);
        }
    }
#endif
}
