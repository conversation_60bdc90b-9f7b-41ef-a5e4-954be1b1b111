using BBB.CellTypes;
using BBB.Match3.Systems.CreateSimulationSystems;
using UnityEngine;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Renderer
{
    public sealed class TntCellLayer : CellLayerBase
    {
        public int Count { get; private set; }

        public int SizeX { get; private set; }

        public int SizeY { get; private set; }

        public TileKinds Kind { get; private set; }

        public TntTargetType Target { get; private set; }

        public override CellOverlayType OverlayType => CellOverlayType.TntOverlay;

        public override bool IsAnimated => true;

        public override CellLayerState State => CellLayerState.Tnt;

        protected override bool IsCondition(Cell cell)
        {
            return cell.IsAnyOf(CellState.Tnt);
        }

        protected override void Customize(Cell cell)
        {
            base.Customize(cell);
            Count = cell.TntCount;
            Kind = cell.TntTarget == TntTargetType.Simple ? cell.TntKind : TileKinds.Undefined;
            SizeX = cell.SizeX;
            SizeY = cell.SizeY;
            Target = cell.TntTarget;
            Coords = cell.Coords;
        }

        public static void HandleTnt(Cell cell, Grid grid, Coords sourceCoords,
            IRootSimulationHandler events, GoalsSystem goalsSystem, HitWaitParams hitWaitParams)
        {
            cell.TntCount--;
            events.AddAction(new ActionReduceTntCount(cell.Coords, sourceCoords, cell.SizeX, cell.SizeY,
                hitWaitParams));
            
            if (cell.TntCount > 0)
            {
                return;
            }

            if (!cell.IsAnyOf(CellState.Tnt))
            {
                return;
            }

            MarkSandAsDestroyedIfTntIsDestroyedThisTurn(cell, grid);

            var sizeX = cell.SizeX;
            var sizeY = cell.SizeY;

            cell.TntCount = 0;
            cell.Remove(CellState.Tnt);
            cell.SizeX = 0;
            cell.SizeY = 0;
            grid.RefrehsAllCellsMultisizeCaches();
            goalsSystem.TryReduceGoalIfNeeded(GoalType.Tnt);

            cell.IsBusy += 0;

            events.AddAction(new ActionReduceTntCount(cell.Coords, cell.Coords, sizeX, sizeY));
        }

        //This prevents the Sand expand on the same turn as TNT is destroyed
        //the Sand should wait at least until next movement to start expanding,
        //as player couldn’t have possibly interacted with Sand while Sand is hidden under TNT
        private static void MarkSandAsDestroyedIfTntIsDestroyedThisTurn(Cell cell, Grid grid)
        {
            var sizeX = Mathf.Max(1, cell.SizeX);
            var sizeY = Mathf.Max(1, cell.SizeY);

            for (var x = 0; x < sizeX; x++)
            {
                for (var y = 0; y < sizeY; y++)
                {
                    var coords = new Coords(cell.Coords.X + x, cell.Coords.Y + y);
                    if (!grid.TryGetCell(coords, out var affectedCell)) continue;

                    if (!affectedCell.HasTile() || !affectedCell.Tile.IsAnyOf(TileState.SandMod)) continue;
                    grid.SandHandler.MarkSandWasDestroyedThisTurn();
                    return;
                }
            }
        }
        
        public static void NotifyTnt(IRootSimulationHandler events,
            Cell tntCell, Coords sourceCoords, HitWaitParams hitWaitParams)
        {
            tntCell.TntCount--;
            if (tntCell.TntCount <= 0)
            {
                TileCollectorsHandler.AddToDieReactionCache(tntCell, hitWaitParams);
            }
            events.AddAction(new ActionReduceTntCount(tntCell.Coords, sourceCoords, tntCell.SizeX, tntCell.SizeY, hitWaitParams));
        }
        
        public static bool IsTileMatchTargetType(TntTargetType targetType, TileKinds tntKind, TileAsset tileAsset,
            TileKinds tileKind)
        {
            switch (targetType)
            {
                case TntTargetType.Simple:
                    if (tileAsset != TileAsset.Simple) return false;
                    if (tntKind is TileKinds.None or TileKinds.Undefined) return true;

                    return tntKind == tileKind;

                case TntTargetType.BoosterAny:
                    return tileAsset is TileAsset.Bomb or TileAsset.ColorBomb or TileAsset.RowBreaker or TileAsset
                        .ColumnBreaker or TileAsset.Propeller;
                case TntTargetType.BoosterLineBreaker:
                    return tileAsset is TileAsset.RowBreaker or TileAsset.ColumnBreaker;
                case TntTargetType.BoosterBomb:
                    return tileAsset == TileAsset.Bomb;
                case TntTargetType.BoosterBolt:
                    return tileAsset == TileAsset.ColorBomb;
                case TntTargetType.DropItem:
                    return tileAsset == TileAsset.DropItem;
                case TntTargetType.Litter:
                    return tileAsset == TileAsset.Litter;
                case TntTargetType.Pinata:
                    return tileAsset == TileAsset.Pinata;
                case TntTargetType.Sticker:
                    return tileAsset == TileAsset.Sticker;
                case TntTargetType.ColorCrate:
                    return tileAsset == TileAsset.ColorCrate;
                case TntTargetType.Watermelon:
                    return tileAsset == TileAsset.Watermelon;
                case TntTargetType.MoneyBag:
                    return tileAsset == TileAsset.MoneyBag;
                case TntTargetType.Egg:
                    return tileAsset == TileAsset.Egg;
                case TntTargetType.FlowerPot:
                    return tileAsset == TileAsset.FlowerPot;
                case TntTargetType.Bird:
                    return tileAsset == TileAsset.Bird;
                case TntTargetType.Sheep:
                    return tileAsset == TileAsset.Sheep;
                case TntTargetType.Banana:
                    return tileAsset == TileAsset.Banana;
                case TntTargetType.Monkey:
                    return tileAsset == TileAsset.Monkey;
                case TntTargetType.BigMonkey:
                    return tileAsset == TileAsset.BigMonkey;
                case TntTargetType.Hen:
                    return tileAsset == TileAsset.Hen;
                case TntTargetType.Chicken:
                    return tileAsset == TileAsset.Chicken;
                case TntTargetType.Bee:
                    return tileAsset == TileAsset.Bee;
                case TntTargetType.Mole:
                    return tileAsset == TileAsset.Mole;
                case TntTargetType.Squid:
                    return tileAsset == TileAsset.Squid;
            }

            return false;
        }
    }
}