using System;
using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic.Tiles;
using UnityEngine;
using Random = UnityEngine.Random;

namespace BBB.Match3.Renderer
{
    public class SlotMachineItemRenderer : BbbMonoBehaviour
    {
        // List of all possible slot tokens in the machine
        [SerializeField] private List<SlotMachineToken> slotTokens;

        // Sets the final state of each slot token based on the given outcome
        public void SetFinalState(SlotMachineRewardType finalOutcome, TileAsset rewardAsset)
        {
            foreach (var slotToken in slotTokens)
            {
                slotToken.FinalReward = finalOutcome;
                slotToken.RewardAsset = rewardAsset;
                slotToken.ApplyOutcome();
            }
        } 
        
        // Resets the state of all slot tokens
        public void ResetAllStates(Dictionary<int, Sprite> spritesForReels)
        {
            foreach (var slotToken in slotTokens)
            {
                slotToken.FinalReward = SlotMachineRewardType.None;
                slotToken.RewardAsset = TileAsset.Undefined;
                slotToken.ResetStates(spritesForReels);
            }
        }
    }

    [Serializable]
    public class SlotMachineToken
    {
        [Serializable]
        public struct RewardSpritePair
        {
            public TileAsset RewardType;
            public SpriteRenderer Sprite;
        }

        private const int FinalSlotIndex = 4;
        private const int OutcomeRange = 5;
        public int Index;
        public List<RewardSpritePair> RewardSprites;

        public SlotMachineRewardType FinalReward { get; set; }
        public TileAsset RewardAsset { get; set; }

        private Dictionary<int, Sprite> _spritesForReels;

        // Applies the outcome to the slot token
        public void ApplyOutcome()
        {
            if (FinalReward == SlotMachineRewardType.None) return;

            ResetStates(_spritesForReels);
            
            if (Index == FinalSlotIndex)
            {
                foreach (var pair in RewardSprites)
                {
                    if (pair.RewardType == RewardAsset)
                    {
                        pair.Sprite.enabled = true;
                        return;
                    }
                }
            }
            else
            {
                var randomIndex = Random.Range(0, OutcomeRange);
                RewardSprites[randomIndex].Sprite.enabled = true;
            }
        }

        // Resets the state of all reward sprites
        public void ResetStates(Dictionary<int, Sprite> spritesForReels)
        {
            _spritesForReels = spritesForReels;
            
            foreach (var pair in RewardSprites)
            {
                pair.Sprite.sprite = _spritesForReels[(int) pair.RewardType];
                pair.Sprite.enabled = false;
            }
        }
    }
}