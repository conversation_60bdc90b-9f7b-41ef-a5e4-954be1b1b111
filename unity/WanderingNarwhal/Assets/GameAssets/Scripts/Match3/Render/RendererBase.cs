using BBB.DI;
using BBB.UI;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public abstract class RendererBase : BbbMonoBehaviour, IContextInitializable
    {
        private LevelHolder _levelHolder;
        protected TilesResources TilesResourcesRef;

        protected Grid CurrentGrid => _levelHolder.level.Grid;

        protected Vector2 GridSize
        {
            get
            {
                var grid = _levelHolder.level.Grid;
                var cellSize = TilesResourcesRef.CellSize;
                return grid.Size.Multiply(cellSize);
            }
        }

        protected Vector2 CellSize
        {
            get { return TilesResourcesRef.CellSize; }
        }

        public virtual void InitializeByContext(IContext context)
        {
            if (TilesResourcesRef == null)
            {
                TilesResourcesRef = context.Resolve<TilesResources>();
                _levelHolder = context.Resolve<LevelHolder>();
            }
        }
    }
}