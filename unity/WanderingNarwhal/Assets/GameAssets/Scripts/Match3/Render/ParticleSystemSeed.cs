using UnityEngine;

namespace BBB.Match3.Renderer
{
    [ExecuteAlways]
    public class ParticleSystemSeed : BbbMonoBehaviour
    {
        [SerializeField]
        private uint _seed;

        [SerializeField] private ParticleSystem[] _particleSystems;

        private void OnValidate()
        {
            Refresh(false);
        }

        private void Start()
        {
            Refresh(Application.isPlaying);
        }

#if UNITY_EDITOR
        private void Update()
        {
            if (!Application.isPlaying)
            {
                var hasChange = false;

                foreach (var system in _particleSystems)
                    hasChange |= system.randomSeed != _seed;

                if (hasChange)
                    Refresh(false);
            }
        }
#endif

        private void Refresh(bool randomSeed = true)
        {
            if (randomSeed)
                _seed = (uint)Random.Range(uint.MinValue, uint.MaxValue);

            foreach (var system in _particleSystems)
            {
                if (system == null) continue;
                var systemIsPlaying = system.isPlaying;

                if (systemIsPlaying)
                    system.Stop(false, ParticleSystemStopBehavior.StopEmittingAndClear);

                system.useAutoRandomSeed = false;
                system.randomSeed = _seed;

                if (systemIsPlaying)
                    system.Play();
            }
        }
    }
}