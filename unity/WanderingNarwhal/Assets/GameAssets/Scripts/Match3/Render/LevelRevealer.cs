using System;
using System.Collections.Generic;
using BBB.Core.UI;
using BBB.DI;
using UnityEngine;

namespace BBB.UI.Level
{
    public interface ILevelRevealObserver
    {
        void OnLevelRevealStart();
    }

    public interface ILevelRevealer
    {
        void AddObserver(ILevelRevealObserver observer);
        void HideInstant();
        void Hide(Action onDone);
        void Reveal(Action onDone);
        void HideBottomPanel();
        void RevealBottomPanel();
    }
    
    public class LevelRevealer : BbbMonoBehaviour, IContextInitializable, IContextReleasable, ILevelRevealer
    {
        [SerializeField] private UITargetRectTweener _bottomPanelTweener;
        [SerializeField] private UITargetRectTweener[] _tweeners;
        
        private readonly List<ILevelRevealObserver> _observers = new();
        private LevelController _levelController;
        
        public void InitializeByContext(IContext context)
        {
            _observers.Clear();
        }

        public void Init(LevelController levelController)
        {
            _levelController = levelController;
        }

        public void ReleaseByContext(IContext context)
        {
            _observers.Clear();
        }

        public void AddObserver(ILevelRevealObserver observer)
        {
            _observers.Add(observer);
        }

        public void HideInstant()
        {
            if (_tweeners.Length > 0)
                foreach (var tweener in _tweeners)
                {
                    tweener.MoveBackInstant();
                }
        }

        public void Hide(Action onDone)
        {
            if (_tweeners.Length <= 0) return;
            foreach (var tween in _tweeners)
            {
                tween.MoveBack();
            }
        }

        public void Reveal(Action onDone)
        {
            if (_tweeners.Length > 0)
            {
                _tweeners[0].OnForthEnd(onDone);
                
                foreach(var observer in _observers)
                    observer.OnLevelRevealStart();
            
               foreach (var tweener in _tweeners)
                    tweener.MoveForth();
                

            }
            else
                onDone.SafeInvoke();
        }
        
        public void RevealBottomPanel()
        {
            if (_bottomPanelTweener == null) return;
            _bottomPanelTweener.MoveForth();
        }
        
        public void HideBottomPanel()
        {
            if (_bottomPanelTweener == null) return;
            _bottomPanelTweener.MoveBack();
        }
    }
}