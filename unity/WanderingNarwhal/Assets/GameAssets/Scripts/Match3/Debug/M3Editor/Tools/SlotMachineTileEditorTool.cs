#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;
using GameAssets.Scripts.Match3.Logic.Tiles;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class SlotMachineTileEditorTool : TileTool
    {
        private const int SizeX = 3;
        private const int SizeY = 2;
        private const int Hp = 4;

        public SlotMachineTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
            MultiTileElement = new MultiTileTntElement(TntTargetType.SlotMachine, M3EditorTile);
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            if (TryUpdateMultiTile(coords)) return;

            var originCell = M3EditorTile.GetGridCell(coords, false);
            if (originCell == null) return;

            var fitsToGrid = true;
            for (var x = coords.X; x < coords.X + SizeX && fitsToGrid; x++)
            for (var y = coords.Y; y < coords.Y + SizeY; y++)
            {
                var pos = new Coords(x, y);
                var cell = M3EditorTile.GetGridCell(pos, false);
                if (cell != null && !cell.HasMultiSizeCellReferenceWithMultiSizeTile()) continue;
                fitsToGrid = false;
                break;
            }

            if (!fitsToGrid) return;

            for (var x = coords.X; x < coords.X + SizeX; x++)
            for (var y = coords.Y; y < coords.Y + SizeY; y++)
            {
                grid.TryGetCell(new Coords(x, y), out var cell);
                cell.HardRemoveTile(0);
                cell.ClearState(true);
                cell.UpdateCellBackgroundState();
            }

            var tileParams = new List<TileParam>
            {
                new(TileParamEnum.SizeX, SizeX),
                new(TileParamEnum.SizeY, SizeY),
                new(TileParamEnum.AdjacentHp, Hp),
                new(TileParamEnum.SlotMachineOutcome, (int)SlotMachineRewardType.None),
            };

            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++,
                TileAsset.SlotMachine, new TileOrigin(Creator.LevelEditor, originCell), TileKinds.None, tileParams);

            originCell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, Hp);
        }
    }
}
#endif