using System.Collections.Generic;
using Core.Configs;
using FBConfig;
using GameAssets.Scripts.Match3.Logic;

namespace GameAssets.Scripts.Match3.Settings
{
    public class MechanicTargetingSettings
    {
        public Dictionary<string, float> TargetingWeightTable { get; }
        public Dictionary<GoalType, float> GoalTypeRelatedWeights { get; }
        public float WeightUnderDropItem { get; }
        public float DropItemWeightDiminishStep { get; }
        public float TntGoalWeight { get; }
        public float TukTukGoalWeight { get; }
        public float FrameNearAnimalWeight { get; }
        
        private const string FrameNearAnimal = "FrameNearAnimal";

        public MechanicTargetingSettings(MechanicTargetingConfig config)
        {
            var weightTable = FlatBufferHelper.ToDict(config.WeightTable, config.WeightTableLength);
            if (weightTable != null)
            {
                TargetingWeightTable = new Dictionary<string, float>();
                foreach (var kvp in weightTable)
                {
                    var generalizedLayer = kvp.Key;
                    TargetingWeightTable[generalizedLayer] = kvp.Value;
                }
                FrameNearAnimalWeight = TargetingWeightTable[FrameNearAnimal];
            }

            var goalWeightTable = FlatBufferHelper.ToDict(config.GoalWeightTable, config.GoalWeightTableLength);
            if (goalWeightTable != null)
            {
                GoalTypeRelatedWeights = new Dictionary<GoalType, float>();
                foreach (var kvp in goalWeightTable)
                {
                    var goalType = kvp.Key.TryParseToEnum<GoalType>();
                    GoalTypeRelatedWeights[goalType] = kvp.Value;
                }
            }

            WeightUnderDropItem = config.WeightUnderDropItem;
            DropItemWeightDiminishStep = config.DropItemWeightDiminishStep;
            TntGoalWeight = config.TntGoalWeight;
            TukTukGoalWeight = config.TukTukGoalWeight;
        }
    }
}