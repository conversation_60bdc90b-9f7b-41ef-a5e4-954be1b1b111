using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using Core.Configs;
using FBConfig;
using GameAssets.Scripts.Match3.Logic.Tiles;

namespace GameAssets.Scripts.Match3.Settings
{
    public class GiantPinataConfiguration
    {
        public readonly Dictionary<GiantPinataRewardType, float> RewardConfigurations;
        public readonly int MaxSpawnCount;
        public readonly bool AllowSameType;

        public GiantPinataConfiguration(IDictionary<string, GiantPinataOutcomeConfig> outcomeConfigDictionary)
        {
            RewardConfigurations = new Dictionary<GiantPinataRewardType, float>();

            if (outcomeConfigDictionary == null || outcomeConfigDictionary.Count == 0)
            {
                BDebug.LogError(LogCat.Match3, "GiantPinataConfiguration: outcomeConfigDictionary is null.");
                return;
            }

            var configSet = false;

            foreach (var kvp in outcomeConfigDictionary)
            {
                var config = kvp.Value;
                var distributionDict = FlatBufferHelper.ToDict(config.Distribution, config.DistributionLength);
                foreach (var pair in distributionDict)
                {
                    if (string.IsNullOrEmpty(pair.Key))
                    {
                        BDebug.LogError(LogCat.Match3, "GiantPinataConfiguration: key is null.");
                        continue;
                    }

                    if (Enum.TryParse(pair.Key, out GiantPinataRewardType rewardType))
                    {
                        RewardConfigurations.TryAdd(rewardType, pair.Value);
                    }
                    else
                    {
                        BDebug.LogError(LogCat.Match3, $"GiantPinataConfiguration: Unable to parse key '{pair.Key}'.");
                    }
                }

                if (!configSet)
                {
                    MaxSpawnCount = config.Count;
                    AllowSameType = config.AllowSameType;
                    configSet = true;
                }
            }
        }
    }
}