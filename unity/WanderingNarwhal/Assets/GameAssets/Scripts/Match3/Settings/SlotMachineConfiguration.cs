using System;
using System.Collections.Generic;
using FBConfig;
using GameAssets.Scripts.Match3.Logic.Tiles;

namespace GameAssets.Scripts.Match3.Settings
{
    public class SlotMachineConfiguration
    {
        public readonly Dictionary<SlotMachineRewardType, SlotMachineOutcomeConfig> RewardConfigurations;

        public SlotMachineConfiguration(IDictionary<string, SlotMachineOutcomeConfig> outcomeConfigDictionary)
        {
            RewardConfigurations = new Dictionary<SlotMachineRewardType, SlotMachineOutcomeConfig>();

            foreach (var kvp in outcomeConfigDictionary)
            {
                var configKey = kvp.Key;
                // Try to parse the key as an enum value of SlotMachineRewardType
                if (Enum.TryParse(configKey, out SlotMachineRewardType rewardType))
                {
                    // Add the parsed enum value and corresponding config to the dictionary
                    RewardConfigurations.Add(rewardType, outcomeConfigDictionary[configKey]);
                }
            }
        }
    }

}