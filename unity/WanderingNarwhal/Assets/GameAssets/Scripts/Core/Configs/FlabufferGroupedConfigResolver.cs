using System;
using System.Collections.Generic;
using System.Diagnostics;
using BBB.Core.Analytics.TechAnalytics.Managers;
using Cysharp.Threading.Tasks;
using FBConfig;
using RPC.Core;

namespace Core.Configs
{
    public class FlatBufferGroupedConfigResolver : FlatBufferConfigResolverBase<FlatbufferConfigDict, FlatbufferConfig>
    {
        public static readonly FlatBufferGroupedConfigResolver Instance = new();

        private Dictionary<string, IFlatbufferWrapper> _config;

        private readonly HashSet<Type> _updatedConfigs = new();

        public FlatBufferGroupedConfigResolver() : base()
        {
        }

        public FlatBufferGroupedConfigResolver(string name) : base(name)
        {
        }

        public override void Parse(IDictionary<string, object> result, bool fromServer)
        {
            _config = ProcessConfigLegacy(result, GetName(), fromServer);
        }

        public override async UniTask Parse(Dictionary<string, ConfigVersionInfo> configs, bool async, bool fromServer)
        {
            _config = await ProcessConfig(configs, GetName(), async, fromServer);
        }

        public override IDictionary<string, T1> GetConfig<T1>()
        {
            return _config.TryGetValue(ConfigUtils.GetKey<T1>(), out var config) ? config as IDictionary<string, T1> : null;
        }

        private Dictionary<string, IFlatbufferWrapper> ProcessConfigLegacy(IDictionary<string, object> configs, string configName, bool fromServer)
        {
            _configParse = _flatbufferLoader.ParseConfig<FlatbufferConfigDict>(configs, configName, fromServer);
            return CreateConfig(_configParse, configName);
        }

        protected virtual async UniTask<Dictionary<string, IFlatbufferWrapper>> ProcessConfig(Dictionary<string, ConfigVersionInfo> configs, string configName, bool async, bool fromServer)
        {
            _configParse = await _flatbufferLoader.ParseConfigAsync<FlatbufferConfigDict>(configs, configName, async, fromServer);
            var startDateTime = MetricsManagerHelper.DateTimeOffsetUtcNowBasedOnPause;
            var stopwatch = Stopwatch.StartNew();
            var config = CreateConfig(_configParse, configName);
            stopwatch.Stop();
            UnityEngine.Debug.Log($"#### CreateConfig {stopwatch.ElapsedMilliseconds}ms ({stopwatch.ElapsedTicks} ticks)");
            LoadingTimeMetricsManager.ReportConfigParse(Name, startDateTime);
            return config;
        }

        private Dictionary<string, IFlatbufferWrapper> CreateConfig(FlatbufferConfigDict? configParse, string keyName)
        {
            var groupedConfigs = new Dictionary<string, IFlatbufferWrapper>();
            _updatedConfigs.Clear();

            if (configParse == null)
                return groupedConfigs;

            var configWrapper = new FlatBufferLazyDictWrapper<FlatbufferConfigDict, FlatbufferConfig, FlatbufferConfigT>(configParse)["default"];

            AddConfigToGroup(groupedConfigs, new AudioMixerConfigWrapper(configWrapper), typeof(AudioMixerConfig));
            AddConfigToGroup(groupedConfigs, new CountriesTiersConfigWrapper(configWrapper), typeof(CountriesTiersConfig));
            AddConfigToGroup(groupedConfigs, new IAPBasketConfigWrapper(configWrapper), typeof(IAPBasketConfig));
            AddConfigToGroup(groupedConfigs, new NarrativeDialogConfigWrapper(configWrapper), typeof(NarrativeDialogConfig));
            AddConfigToGroup(groupedConfigs, new DailyTriviaConfigWrapper(configWrapper), typeof(DailyTriviaConfig));
            AddConfigToGroup(groupedConfigs, new LevelAdsConfigWrapper(configWrapper), typeof(LevelAdsConfig));
            AddConfigToGroup(groupedConfigs, new GameEventConfigWrapper(configWrapper), typeof(GameEventConfig));
            AddConfigToGroup(groupedConfigs, new RaceGameEventConfigWrapper(configWrapper), typeof(RaceGameEventConfig));
            AddConfigToGroup(groupedConfigs, new RoyaleGameEventConfigWrapper(configWrapper), typeof(RoyaleGameEventConfig));
            AddConfigToGroup(groupedConfigs, new GameEventMetaConfigWrapper(configWrapper), typeof(GameEventMetaConfig));
            AddConfigToGroup(groupedConfigs, new IAPStoreCategoryConfigWrapper(configWrapper), typeof(IAPStoreCategoryConfig));
            AddConfigToGroup(groupedConfigs, new IAPStoreVirtualItemPackConfigWrapper(configWrapper), typeof(IAPStoreVirtualItemPackConfig));
            AddConfigToGroup(groupedConfigs, new OfferConfigWrapper(configWrapper), typeof(OfferConfig));
            AddConfigToGroup(groupedConfigs, new BoosterConfigWrapper(configWrapper), typeof(BoosterConfig));
            AddConfigToGroup(groupedConfigs, new GachaConfigWrapper(configWrapper), typeof(GachaConfig));
            AddConfigToGroup(groupedConfigs, new Match3SpecialVoiceoversConfigWrapper(configWrapper), typeof(Match3SpecialVoiceoversConfig));
            AddConfigToGroup(groupedConfigs, new LivesConfigWrapper(configWrapper), typeof(LivesConfig));
            AddConfigToGroup(groupedConfigs, new HintSystemConfigWrapper(configWrapper), typeof(HintSystemConfig));
            AddConfigToGroup(groupedConfigs, new SuperBoostProgressConfigWrapper(configWrapper), typeof(SuperBoostProgressConfig));
            AddConfigToGroup(groupedConfigs, new SuperBoostConfigWrapper(configWrapper), typeof(SuperBoostConfig));
            AddConfigToGroup(groupedConfigs, new ScreenConfigWrapper(configWrapper), typeof(ScreenConfig));
            AddConfigToGroup(groupedConfigs, new FakeUsersConfigWrapper(configWrapper), typeof(FakeUsersConfig));
            AddConfigToGroup(groupedConfigs, new LocalNotificationsConfigWrapper(configWrapper), typeof(LocalNotificationsConfig));
            AddConfigToGroup(groupedConfigs, new LocalPushNotificationsTimingConfigWrapper(configWrapper), typeof(LocalPushNotificationsTimingConfig));
            AddConfigToGroup(groupedConfigs, new SystemConfigWrapper(configWrapper), typeof(SystemConfig));
            AddConfigToGroup(groupedConfigs, new CompetitionGameEventConfigWrapper(configWrapper), typeof(CompetitionGameEventConfig));
            AddConfigToGroup(groupedConfigs, new ChallengeTriviaRewardsConfigWrapper(configWrapper), typeof(ChallengeTriviaRewardsConfig));
            AddConfigToGroup(groupedConfigs, new ChallengeConfigWrapper(configWrapper), typeof(ChallengeConfig));
            AddConfigToGroup(groupedConfigs, new UnifiedPromotionConfigWrapper(configWrapper), typeof(UnifiedPromotionConfig));
            AddConfigToGroup(groupedConfigs, new ThemeConfigWrapper(configWrapper), typeof(ThemeConfig));
            AddConfigToGroup(groupedConfigs, new ThemeMetaConfigWrapper(configWrapper), typeof(ThemeMetaConfig));
            AddConfigToGroup(groupedConfigs, new DefaultNamesConfigWrapper(configWrapper), typeof(DefaultNamesConfig));
            AddConfigToGroup(groupedConfigs, new EndlessTreasureConfigWrapper(configWrapper), typeof(EndlessTreasureConfig));
            AddConfigToGroup(groupedConfigs, new RaceStageConfigWrapper(configWrapper), typeof(RaceStageConfig));
            AddConfigToGroup(groupedConfigs, new TeamEventConfigWrapper(configWrapper), typeof(TeamEventConfig));
            AddConfigToGroup(groupedConfigs, new PlayerSkillConfigWrapper(configWrapper), typeof(PlayerSkillConfig));
            AddConfigToGroup(groupedConfigs, new WeeklyLeaderboardConfigWrapper(configWrapper), typeof(WeeklyLeaderboardConfig));
            AddConfigToGroup(groupedConfigs, new AdPlacementConfigWrapper(configWrapper), typeof(AdPlacementConfig));
            AddConfigToGroup(groupedConfigs, new ButlerGiftConfigWrapper(configWrapper), typeof(ButlerGiftConfig));
            AddConfigToGroup(groupedConfigs, new AssistSystemConfigWrapper(configWrapper), typeof(AssistSystemConfig));
            AddConfigToGroup(groupedConfigs, new SceneTaskConfigWrapper(configWrapper), typeof(SceneTaskConfig));
            AddConfigToGroup(groupedConfigs, new DailyTaskSettingsConfigWrapper(configWrapper), typeof(DailyTaskSettingsConfig));
            AddConfigToGroup(groupedConfigs, new CarrotsConfigWrapper(configWrapper), typeof(CarrotsConfig));
            AddConfigToGroup(groupedConfigs, new SdbConfigWrapper(configWrapper), typeof(SdbConfig));
            AddConfigToGroup(groupedConfigs, new SweepStakesGameEventConfigWrapper(configWrapper), typeof(SweepStakesGameEventConfig));
            AddConfigToGroup(groupedConfigs, new SweepstakesVideoConfigWrapper(configWrapper), typeof(SweepstakesVideoConfig));
            AddConfigToGroup(groupedConfigs, new IAPStoreMarketItemConfigWrapper(configWrapper), typeof(IAPStoreMarketItemConfig));
            AddConfigToGroup(groupedConfigs, new QuestConfigWrapper(configWrapper), typeof(QuestConfig));
            AddConfigToGroup(groupedConfigs, new SlotMachineOutcomeConfigWrapper(configWrapper), typeof(SlotMachineOutcomeConfig));
            AddConfigToGroup(groupedConfigs, new MechanicTargetingConfigWrapper(configWrapper), typeof(MechanicTargetingConfig));
            AddConfigToGroup(groupedConfigs, new GiantPinataOutcomeConfigWrapper(configWrapper), typeof(GiantPinataOutcomeConfig));
            AddConfigToGroup(groupedConfigs, new LockItemConfigWrapper(configWrapper), typeof(LockItemConfig));
            AddConfigToGroup(groupedConfigs, new GameUpdateConfigWrapper(configWrapper), typeof(GameUpdateConfig));
            AddConfigToGroup(groupedConfigs, new SocialConfigWrapper(configWrapper), typeof(SocialConfig));
            AddConfigToGroup(groupedConfigs, new LevelNarrativeConfigWrapper(configWrapper), typeof(LevelNarrativeConfig));
            AddConfigToGroup(groupedConfigs, new IceBreakerConfigWrapper(configWrapper), typeof(IceBreakerConfig));
            AddConfigToGroup(groupedConfigs, new AutoPopupPriorityConfigWrapper(configWrapper), typeof(AutoPopupPriorityConfig));
            AddConfigToGroup(groupedConfigs, new HudAssetReferenceConfigWrapper(configWrapper), typeof(HudAssetReferenceConfig));
            AddConfigToGroup(groupedConfigs, new HudConfigWrapper(configWrapper), typeof(HudConfig));
            AddConfigToGroup(groupedConfigs, new CollectionConfigWrapper(configWrapper), typeof(CollectionConfig));
            AddConfigToGroup(groupedConfigs, new TutorialConfigWrapper(configWrapper), typeof(TutorialConfig));
            AddConfigToGroup(groupedConfigs, new DailyTasksConfigWrapper(configWrapper), typeof(DailyTasksConfig));
            AddConfigToGroup(groupedConfigs, new ChallengeTriviaConfigWrapper(configWrapper), typeof(ChallengeTriviaConfig));
            AddConfigToGroup(groupedConfigs, new CollectionCardsConfigWrapper(configWrapper), typeof(CollectionCardsConfig));
            AddConfigToGroup(groupedConfigs, new ScenesConfigWrapper(configWrapper), typeof(ScenesConfig));
            AddConfigToGroup(groupedConfigs, new ChallengeLocationConfigWrapper(configWrapper), typeof(ChallengeLocationConfig));
            AddConfigToGroup(groupedConfigs, new SweepstakesDailyLoginConfigWrapper(configWrapper), typeof(SweepstakesDailyLoginConfig));
            AddConfigToGroup(groupedConfigs, new QuickActionsConfigWrapper(configWrapper), typeof(QuickActionsConfig));

            return groupedConfigs;
        }

        private void AddConfigToGroup(Dictionary<string, IFlatbufferWrapper> groupedConfigs, IFlatbufferWrapper wrapper, Type configType)
        {
            var configKey = ConfigUtils.GetKey(configType);
            if (_config == null || !_config.ContainsKey(configKey) || _config[configKey].Hash != wrapper.Hash)
            {
                _updatedConfigs.Add(configType);
            }
            groupedConfigs[configKey] = wrapper;
        }

        public override void AddConfigResolvers(IDictionary<string, IConfigResolver> configLoaders, string name = null)
        {
            base.AddConfigResolvers(configLoaders, name);
            foreach (var config in _config)
            {
                configLoaders[config.Key] = this;
            }
        }

        public override Type GetWrapperDictType(Type configType)
        {
            return _config?.TryGetValue(ConfigUtils.GetKey(configType), out var wrapper) == true ? wrapper.GetType() : typeof(IDictionary<string, object>);
        }

        public override void AddToUpdatedConfigs(HashSet<Type> updatedConfigs)
        {
            updatedConfigs.AddRange(_updatedConfigs);
        }
    }
}