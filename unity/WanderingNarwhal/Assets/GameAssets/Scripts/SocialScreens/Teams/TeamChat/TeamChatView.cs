using System;
using System.Collections.Generic;
using BBB;
using BBB.Audio;
using BBB.BrainCloud;
using BBB.Core;
using BBB.DI;
using BBB.MMVibrations;
using BBB.MMVibrations.Plugins;
using BBB.UI;
using BBB.UI.Core;
using BebopBee;
using BebopBee.Core;
using BebopBee.Core.Audio;
using Bebopbee.Core.Extensions.Unity;
using GameAssets.Scripts.SocialScreens.Teams.Screens;
using GameAssets.Scripts.SocialScreens.Teams.Screens.LogoSelectionScreen;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat.MessageActions;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages;
using GameAssets.Scripts.SocialScreens.Teams.Utils;
using RPC.Teams;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using BBB.Social.Chat;
using BBB.Social;
using BBB.TeamEvents;
using BebopBee.Core.UI;
using DG.Tweening;
using GameAssets.Scripts.UI.OverlayDialog;

namespace GameAssets.Scripts.SocialScreens.Teams.TeamChat
{
    public class TeamChatView : ContextedUiBehaviour
    {
        private const string TeamChatHeaderTeamMembersOnline = "TEAM_CHAT_HEADER_TEAM_MEMBERS_ONLINE";

        private const string TeamChatSingleTyping = "TEAM_CHAT_SINGLE_TYPING";
        private const string TeamChatMultipleTyping = "TEAM_CHAT_MULTIPLE_TYPING";
        private const string TeamChatCharacterLimitExceeded = "TEAM_CHAT_CHARACTER_LIMIT_EXCEEDED";
        private const float DefaultEditorAnimationTime = 0.25f;

        [SerializeField] private ReactionsConfig _reactionsConfig;
        [SerializeField] private MessageContextDialog _messageContextDialog;

        [SerializeField] private GameObject[] _rootHolder;
        [Header("Team info")] [SerializeField] private Button _showTeamInfoButton;
        [SerializeField] private TeamLogoView _teamLogoView;
        [SerializeField] private TextMeshProUGUI _teamName;
        [SerializeField] private TextMeshProUGUI _teamUid;
        [SerializeField] private TextMeshProUGUI _teamMembersOnline;
        [SerializeField] private GameObject _teamMembersHolder;
        [SerializeField] private TextMeshProUGUI _teamMembersTyping;
        [SerializeField] private Button _ranksButton;

        [Header("Ask lives")] [SerializeField] private Button _askLivesButton;
        [SerializeField] private GameObject _askLivesLockedHolder;
        [SerializeField] private GameObject _askLivesUnlockedHolder;
        [SerializeField] private GameObject _maxLivesTextHolder;
        [SerializeField] private GameObject _askLivesClockCountdownTextHolder;
        [SerializeField] private ClockCountdownText _askLivesClockCountdownText;
        [SerializeField] private GameObject _askLivesLockedNormalStateHolder;
        [SerializeField] private Transform _askLivesFloatingTextAnchor;

        [Header("Nudge")] [SerializeField] private Button _nudgeButton;
        [SerializeField] private GameObject _nudgeDisabledHolder;
        [SerializeField] private GameObject _nudgeEnabledHolder;
        [SerializeField] private GameObject _nudgeDisabledDefaultHolder;
        [SerializeField] private GameObject _nudgeDisabledCooldownHolder;
        [SerializeField] private ClockCountdownText _nudgeClockCountdownText;

        [Header("Trivia")]
        [SerializeField] private Button _triviaButton;
        [SerializeField] private NotifierWidget _triviaNotifierWidget;
        [SerializeField] private GameObject _triviaEnabledHolder;
        [SerializeField] private GameObject _triviaDisabledHolder;
        [SerializeField] private Transform _floatingTextAnchor;

        [Header("Attachment")] [SerializeField]
        private Button _attachmentButton;
        [SerializeField] private GameObject _attachmentHolder;
        [SerializeField] private GameObject _attachmentLoadingHolder;

        [SerializeField] private Button _messageButton;

        [SerializeField] private float _debugKeyboardHeightRatio = 0.4f;
        [SerializeField] private RectTransform _referenceSizeDelta;
        [SerializeField] private RectTransform _touchScreenKeyboardAdaptiveArea;
        [SerializeField] private float _defaultBottomOffset = 120f;
        [SerializeField] private float _realExtraOffset = 120f;
        [SerializeField] private TMP_InputField _messageInputField;
        [SerializeField] private RectTransform _messageInputFieldCanvasGroup;
        [SerializeField] private GameObject[] _actionButtonsHolder;

        [SerializeField] private ScrollRect _scrollRectToRefresh;
        [SerializeField] private MessageViewFactory _messageViewFactory;
        [SerializeField] private AttachmentScreen _attachmentScreen;
        [SerializeField] private AttachmentMessageInteractions _attachmentMessageInteractions;
        [SerializeField] private OverlayDialogConfig _overlayDialogConfig;
        [SerializeField] private int _maxMessageCharacterLimit = 500;

        private float _currentKeyboardHeightRatio;
        private Action<string, Action> _sendMessageRequestCallback;
        private Action<ChatMessage, Action, Action<string>> _askForLivesHelpRequestCallback;
        private Action<ChatMessage, string, Dictionary<string, int>, Action, Action> _claimIapRequestCallback;

        private Action _requestGallery;
        private Action _showTeamInfoCallback;
        private Action<ChatMessage, string, Action> _reactionSelectedCallback;
        private Action<ChatMessage> _deleteMessageCallback;
        private Action<ChatMessage> _flagMessageCallback;
        private Action<Action> _nudgeTeamEventCallback;

        private ILocalizationManager _localizationManager;
        private ISocialManager _socialManager;
        private TeamData _currentTeam;
        private IVibrationsWrapper _vibrations;
        private ILeaderboardManager _leaderboardManager;
        private IChatManager _chatManager;
        private ITeamEventManager _teamEventManager;
        private ChallengeTriviaManager _challengeTriviaManager;
        private GameNotificationManager _notificationManager;
        private ILockManager _lockManager;
        private IOverlayDialogManager _overlayDialogManager;
        private bool _chatConnected;
        private CustomChatKeyboard _customKeyboard;
        private const string messageSendErrorDefault = "OFFLINE_CONNECTION_PROBLEM";
        public MessageContextDialog MessageContextDialog => _messageContextDialog;

        protected override void Awake()
        {
            base.Awake();

            _triviaButton.ReplaceOnClick(TriviaButtonHandler);
            _ranksButton.ReplaceOnClick(TriviaButtonHandler);
            _askLivesButton.ReplaceOnClick(AskLivesButtonHandler);
            _nudgeButton.ReplaceOnClick(NudgeButtonHandler);
            _messageButton.ReplaceOnClick(MessageButtonHandler);
            _attachmentButton.ReplaceOnClick(GalleryButtonHandler);
            _showTeamInfoButton.ReplaceOnClick(ShowTeamInfoButtonHandler);

            _messageInputField.characterLimit = _maxMessageCharacterLimit;
            _messageInputField.onValidateInput = OnValidateInput;

            _messageInputField.onSelect.RemoveAllListeners();
            _messageInputField.onSelect.AddListener((x) => { _chatManager.IsTyping = true; });

            _messageInputField.onDeselect.RemoveAllListeners();
            _messageInputField.onDeselect.AddListener((x) =>
            {
                _chatManager.IsTyping = false;
#if UNITY_EDITOR
                _actionButtonsHolder.Enable(true);
                DeAnchorToTouchScreenKeyboard(DefaultEditorAnimationTime);
#endif
            });

            _messageInputField.onSubmit.RemoveAllListeners();
            _messageInputField.onSubmit.AddListener((x) => SendMessageButtonHandler());
            _actionButtonsHolder.Enable(true);
            _reactionsConfig.Init();

            _messageInputFieldCanvasGroup.anchoredPosition = Application.isEditor || !Application.isMobilePlatform ? Vector2.zero : new Vector2(-1000, 0f);
            _teamMembersOnline.text = string.Empty;
        }

        protected override void OnEnable()
        {
            base.OnEnable();

            if (_customKeyboard == null)
            {
                _customKeyboard = new CustomChatKeyboard();
            }

            ResetAnchorScreenKeyboard();
        }

        private void ResetAnchorScreenKeyboard()
        {
            _actionButtonsHolder.Enable(true);
            _touchScreenKeyboardAdaptiveArea.sizeDelta = new Vector2(0f, -_defaultBottomOffset);
            _scrollRectToRefresh.verticalNormalizedPosition = 0;
            _currentKeyboardHeightRatio = 0f;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _customKeyboard.OnKeyboardVisible -= OnKeyboardVisible;
            _customKeyboard.OnKeyboardHidden -= OnKeyboardHidden;
            _customKeyboard = null;
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _localizationManager = context.Resolve<ILocalizationManager>();
            _socialManager = context.Resolve<ISocialManager>();
            _vibrations = context.Resolve<IVibrationsWrapper>();
            _leaderboardManager = context.Resolve<ILeaderboardManager>();
            _chatManager = context.Resolve<IChatManager>();
            _teamEventManager = context.Resolve<ITeamEventManager>();
            _challengeTriviaManager = context.Resolve<ChallengeTriviaManager>();
            _notificationManager = context.Resolve<GameNotificationManager>();
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
            _lockManager = context.Resolve<ILockManager>();
        }

        private void RefreshTypingState(Dictionary<string, ChatPresence> typingUsers)
        {
            if (typingUsers.Count > 0)
            {
                string firstUserName = null;

                if (typingUsers.Count == 1)
                {
                    foreach (var user in typingUsers)
                    {
                        firstUserName = user.Value.User.Name;
                        break;
                    }

                    _teamMembersTyping.text = _localizationManager.getLocalizedTextWithArgs(TeamChatSingleTyping, firstUserName);
                }
                else
                {
                    _teamMembersTyping.text = _localizationManager.getLocalizedTextWithArgs(TeamChatMultipleTyping, typingUsers.Count);
                }

                _teamMembersHolder.SetActive(true);
            }
            else
            {
                _teamMembersHolder.SetActive(false);
            }
        }

        private void AnchorToTouchScreenKeyboard(float time, float heightRatio)
        {
            _currentKeyboardHeightRatio = heightRatio;

            var size = _referenceSizeDelta.sizeDelta;
            var bottomOffset = size.y * _currentKeyboardHeightRatio;

            DOTween.To(() => _touchScreenKeyboardAdaptiveArea.sizeDelta, x =>
            {
                _touchScreenKeyboardAdaptiveArea.sizeDelta = x;
                _scrollRectToRefresh.verticalNormalizedPosition = 0;
            }, new Vector2(0f, Mathf.Min(-bottomOffset + _realExtraOffset, -_defaultBottomOffset)), time);
        }

        private void DeAnchorToTouchScreenKeyboard(float time)
        {
            _actionButtonsHolder.Enable(true);

            DOTween.To(() => _touchScreenKeyboardAdaptiveArea.sizeDelta, x =>
            {
                _touchScreenKeyboardAdaptiveArea.sizeDelta = x;
                _scrollRectToRefresh.verticalNormalizedPosition = 0;
            }, new Vector2(0f, -_defaultBottomOffset), time);
            _currentKeyboardHeightRatio = 0f;
        }

        public void Setup(Action<string, Action> sendMessageRequestCallback, Action<ChatMessage, Action, Action<string>> askForLivesHelpRequestCallback,
            Action<ChatMessage, string, Dictionary<string, int>, Action, Action> claimIapRequestCallback, Action requestGallery, Action showTeamInfoCallback,
            Action<ChatMessage, string, Action> reactionSelectedCallback, Action<ChatMessage> deleteMessageCallback, Action<ChatMessage> flagMessageCallback, Action<Action> nudgeTeamEventCallback)
        {
            _sendMessageRequestCallback = sendMessageRequestCallback;
            _claimIapRequestCallback = claimIapRequestCallback;
            _askForLivesHelpRequestCallback = askForLivesHelpRequestCallback;
            _requestGallery = requestGallery;
            _showTeamInfoCallback = showTeamInfoCallback;
            _reactionSelectedCallback = reactionSelectedCallback;
            _deleteMessageCallback = deleteMessageCallback;
            _flagMessageCallback = flagMessageCallback;
            _nudgeTeamEventCallback = nudgeTeamEventCallback;
        }

        public void SetupCurrentTeam(TeamData currentTeam)
        {
            _currentTeam = currentTeam;
            _teamName.text = currentTeam.Name;
            _teamUid.text = $"{currentTeam.TeamUid} (debug only)";
            _teamUid.gameObject.SetActive(AppDefinesConverter.BbbDebug);
            _teamLogoView.Setup(currentTeam.Icon);
            _messageViewFactory.UpdateTeamMembers();
        }

        public void Show()
        {
            LazyInit();
            _rootHolder.Enable(true);
            UpdateConnectionState();

            Subscribe();

            RefreshTypingState(_chatManager.TypingUsers);
        }

        public void Hide()
        {
            _rootHolder.Enable(false);
            _messageContextDialog.Hide();
            _attachmentScreen.Hide();

            Unsubscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();

            _chatManager.UserTypingEvent += RefreshTypingState;
            _chatManager.OnlinePresenceUpdated += UpdateOnlineUsers;

            _customKeyboard.OnKeyboardHidden += OnKeyboardHidden;
            _customKeyboard.OnKeyboardVisible += OnKeyboardVisible;

            _socialManager.CanAskLivesUpdated += RefreshAskLivesButton;
        }

        private void Unsubscribe()
        {
            if (_chatManager != null)
            {
                _chatManager.UserTypingEvent -= RefreshTypingState;
                _chatManager.OnlinePresenceUpdated -= UpdateOnlineUsers;
            }

            if (_socialManager != null)
            {
                _socialManager.CanAskLivesUpdated -= RefreshAskLivesButton;
            }

            _askLivesClockCountdownText.TimerExpired -= RefreshAskLivesButton;
            _nudgeClockCountdownText.TimerExpired -= RefreshNudgeButton;

            _customKeyboard.OnKeyboardHidden -= OnKeyboardHidden;
            _customKeyboard.OnKeyboardVisible -= OnKeyboardVisible;
        }

        private void OnKeyboardHidden(float time, string text)
        {
            if (!text.IsNullOrEmpty())
            {
                _messageInputField.text = text;
                SendMessageButtonHandler();
            }

            DeAnchorToTouchScreenKeyboard(time);
        }

        private void OnKeyboardVisible(float time, float ratio)
        {
            AnchorToTouchScreenKeyboard(time, ratio);
        }

        public void ShowChatLoading()
        {
            _messageViewFactory.StartLoading();
            ScrollRectHardAnchorToBottom();
        }

        public void RemoveChatLoading()
        {
            _messageViewFactory.RemoveLoading();
        }

        private void ScrollRectHardAnchorToBottom()
        {
            ForceRecursiveLayoutUpdater.UpdateLayout(_scrollRectToRefresh.transform);
            SetToBottom();

            Rx.InvokeNextFrame(() =>
            {
                SetToBottom();
                Rx.InvokeNextFrame(SetToBottom);
            });
            return;

            void SetToBottom()
            {
                _scrollRectToRefresh.velocity = Vector2.zero;
                _scrollRectToRefresh.verticalNormalizedPosition = 0f;
            }
        }

        public void ShowAttachmentLoading(bool showAttachmentLoading)
        {
            _attachmentHolder.SetActive(!showAttachmentLoading);
            _attachmentLoadingHolder.SetActive(showAttachmentLoading);
        }

        private void RefreshAskLivesButton()
        {
            _askLivesClockCountdownText.TimerExpired -= RefreshAskLivesButton;
            var allowedAsking = _socialManager.CanAskForLives();

            var interactable = allowedAsking;
            var disabled = !allowedAsking;

            _askLivesUnlockedHolder.SetActive(interactable);
            _askLivesLockedHolder.SetActive(disabled);
            _askLivesButton.interactable = interactable;

            if (!disabled)
                return;

            var hasTimer = _socialManager.GetRemainingTimeToAskLives() > 0;

            var timerView = !allowedAsking && hasTimer;
            var maxLivesView = !allowedAsking && !hasTimer;

            _askLivesLockedNormalStateHolder.SetActive(allowedAsking);
            _askLivesClockCountdownTextHolder.SetActive(timerView);
            _maxLivesTextHolder.SetActive(maxLivesView);

            if (!timerView)
                return;

            _askLivesClockCountdownText.Init(_localizationManager, _socialManager.GetRemainingTimeToAskLives);
            _askLivesClockCountdownText.TimerExpired += RefreshAskLivesButton;
        }

        private void RefreshNudgeButton()
        {
            var canNudge = _socialManager.CanNudgeTeamCoop();
            var nudgeEnabled = canNudge && _chatConnected;

            _nudgeEnabledHolder.SetActive(nudgeEnabled);

            var cooldown = _socialManager.GetRemainingTimeToNudgeTeamEvent() > 0;

            _nudgeDisabledHolder.SetActive(!nudgeEnabled);
            _nudgeDisabledDefaultHolder.SetActive(!cooldown);
            _nudgeDisabledCooldownHolder.SetActive(cooldown);

            _nudgeClockCountdownText.TimerExpired -= RefreshNudgeButton;

            if (!cooldown) return;

            _nudgeClockCountdownText.Init(_localizationManager, _socialManager.GetRemainingTimeToNudgeTeamEvent);
            _nudgeClockCountdownText.TimerExpired += RefreshNudgeButton;
        }

        private void TriviaButtonHandler()
        {
            if (!_challengeTriviaManager.IsChallengeTriviaEnabled())
            {
                return;
            }

            var isWigglesLocked = _challengeTriviaManager.IsWigglesOnSocialChatLocked();

            if (!isWigglesLocked)
            {
                if (_challengeTriviaManager.TryShowChallengeTriviaModal(_triviaButton.transform, ShowMode.Delayed, () => _socialManager.ShowSocialModal()))
                {
                    _socialManager.HideSocialModal();
                }
            }
            else
            {
                _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
                _overlayDialogConfig.TargetTransform = _floatingTextAnchor;
                _overlayDialogConfig.ShowBackground = true;
                _overlayDialogConfig.TextToDisplay = LocalizationManagerHelper.LevelReachKey;
                _overlayDialogConfig.TextArgs = new object[]
                {
                    _lockManager.LockedReasonText(ChallengeTriviaManager.SocialWigglesLockUid, LockItemType.Social,
                        LockReasonTextType.Short)
                };
                _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
            }
        }

        private void RefreshTriviaButton()
        {
            if (!_challengeTriviaManager.IsChallengeTriviaEnabled())
            {
                _triviaButton.gameObject.SetActive(false);
                return;
            }

            _triviaNotifierWidget.Init(_notificationManager.GetNotifierByType(NotifierType.TriviaChallengeNotifier));

            var isWigglesLocked = _challengeTriviaManager.IsWigglesOnSocialChatLocked();

            _triviaEnabledHolder.SetActive(!isWigglesLocked);
            _triviaDisabledHolder.SetActive(isWigglesLocked);
        }

        private void ShowTeamInfoButtonHandler()
        {
            _socialManager.FetchTeamData();
            _showTeamInfoCallback?.Invoke();
        }

        private void GalleryButtonHandler()
        {
            _requestGallery?.Invoke();
        }

        public void RefreshMessages()
        {
            _messageViewFactory.Setup(_askForLivesHelpRequestCallback, IAPGiftClaimButtonHandler, NudgeTeamCoopClickedHandler, MessageClickHandler, AttachmentClickHandler, LayoutRefreshRequested);
            _messageViewFactory.Clear();

            var messagesList = new List<ChatMessage>(_chatManager.Messages.Values);
            messagesList.Sort((message1, message2) => message1.CreatedAt.CompareTo(message2.CreatedAt));

            foreach (var message in messagesList)
            {
                _messageViewFactory.AddMessage(message, _chatConnected);
            }

            ScrollRectHardAnchorToBottom();
            UpdateOnlineUsers(_chatManager.OnlineUsers);
        }

        private void UpdateOnlineUsers(Dictionary<string, ChatPresence> onlineUsers)
        {
            _teamMembersOnline.text = _localizationManager.getLocalizedTextWithArgs(TeamChatHeaderTeamMembersOnline, onlineUsers.Count, _currentTeam.Members.Count);
        }

        public void Clear()
        {
            _messageViewFactory.Clear();
            _messageContextDialog.Hide();
            _attachmentScreen.Hide();
        }

        public void AddMessage(ChatMessage message)
        {
            _messageViewFactory.AddMessage(message, _chatConnected);
            RefreshScrollRect();

            if (_chatManager.IsLocalUser(message.Sender.Id)) return;

            AudioProxy.PlaySound(GenericSoundIds.ChatMessage);

            BDebug.Log(LogCat.Vibration, $"Playing haptic feedback for incoming chat message -- Vibrations available={_vibrations != null}");
            _vibrations?.PlayHaptic(ImpactPreset.MediumImpact);
        }

        private void RefreshScrollRect()
        {
            ForceRecursiveLayoutUpdater.UpdateLayout(_scrollRectToRefresh.transform);
            _scrollRectToRefresh.verticalNormalizedPosition = 0;
        }

        private void LayoutRefreshRequested()
        {
            var currentNormalizedPosition = _scrollRectToRefresh.verticalNormalizedPosition;
            ForceRecursiveLayoutUpdater.UpdateLayout(_scrollRectToRefresh.transform);
            _scrollRectToRefresh.verticalNormalizedPosition = currentNormalizedPosition;
            _messageContextDialog.RefreshPosition();
        }

        public void UpdateMessage(ChatMessage message)
        {
            _messageViewFactory.UpdateMessage(message, _chatConnected);
            _attachmentMessageInteractions.UpdateMessage(message);
            _messageContextDialog.UpdateMessage(message);
        }

        public void DeleteMessage(ChatMessage message)
        {
            _messageViewFactory.DeleteMessage(message);
            LayoutRefreshRequested();
        }

        private void MessageClickHandler(BaseMessageView messageView)
        {
            var isLocal = _chatManager.IsLocalUser(messageView.Message.Sender.Id);

            if (!_messageContextDialog.IsShown)
            {
                _messageContextDialog.Setup(messageView, isLocal, false,
                    (reactionMessage, reactionUid) => { _reactionSelectedCallback.SafeInvoke(reactionMessage, reactionUid, () => _messageContextDialog.Hide()); }, _deleteMessageCallback,
                    _flagMessageCallback);
                _messageContextDialog.Show();
            }
            else
            {
                _messageContextDialog.Hide();
            }
        }

        public void UpdateConnectionState()
        {
            _chatConnected = _chatManager.IsChatConnected();
            _messageViewFactory.SetConnectionState(_chatConnected);
            UpdateMessageButton();
            RefreshAskLivesButton();
            RefreshNudgeButton();
            RefreshTriviaButton();
        }

        private void HandleLocalMessageRequest(
            MessageType messageType, 
            Action<ChatMessage, Action<bool, BCMessageData>> networkRequestCallback,
            Action<BCMessageData> onSuccessWithData = null,
            string errorMessageKey = messageSendErrorDefault)
        {
            if (!_chatConnected)
            {
                ShowErrorMessage(errorMessageKey, messageType);
                return;
            }

            ChatMessage localMessage = _chatManager.AddLocalMessage(messageType);            
            networkRequestCallback?.Invoke(localMessage, OnRequestComplete);
            
            void OnRequestComplete(bool success, BCMessageData responseData = default)
            {
                if (success)
                {
                    onSuccessWithData?.Invoke(responseData);
                }
                else
                {
                    _chatManager.RemoveLocalMessage(localMessage);
                    ShowErrorMessage(errorMessageKey, messageType);
                }
            }
        }

        private void ShowErrorMessage(string errorMessageKey, MessageType messageType = MessageType.Regular)
        {
            _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
            
            _overlayDialogConfig.TargetTransform = messageType == MessageType.AskForLives 
                ? _askLivesFloatingTextAnchor 
                : _floatingTextAnchor;
            
            _overlayDialogConfig.ShowBackground = true;
            _overlayDialogConfig.TextToDisplay = errorMessageKey;
            _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
        }

        private void AskLivesButtonHandler()
        {
            HandleLocalMessageRequest(
                MessageType.AskForLives,
                (localMessage, callback) => 
                {
                    _socialManager.AskForLives<BCMessageData>((success, responseData) =>
                    {
                        callback(success, responseData);
                    }, localMessage.Id);
                },
                errorMessageKey: LocalizationManagerHelper.OfflineConnectionProblemKey
            );
        }

        private void NudgeButtonHandler()
        {
            if (!_socialManager.CanNudgeTeamCoop())
                return;

            RefreshNudgeButton();
            _nudgeTeamEventCallback.SafeInvoke(RefreshNudgeButton);
        }

        private void IAPGiftClaimButtonHandler(ChatMessage message, string productUid, Dictionary<string, int> reward, Action claimIapSuccess, Action claimIapFailed)
        {
            _claimIapRequestCallback?.Invoke(message, productUid, reward, () =>
            {
                DeleteMessage(message);
                claimIapSuccess?.Invoke();
            }, claimIapFailed);
        }

        private void NudgeTeamCoopClickedHandler(Type eventType)
        {
            var teamEvent = _teamEventManager.GetHighestPriorityEvent();
            if (teamEvent == null || teamEvent.GetType() != eventType)
                return;

            _teamEventManager.TryToShowEvent(teamEvent, !teamEvent.Joined);
            _socialManager.HideSocialModal();
        }

        private void UpdateMessageButton()
        {
            _messageButton.interactable = _chatConnected;
            _attachmentButton.interactable = _chatConnected;
        }

        private void SendMessageButtonHandler()
        {
            if (!_messageInputField.text.Trim().IsNullOrEmpty())
            {
                _sendMessageRequestCallback.SafeInvoke(_messageInputField.text, () => { _messageInputField.text = string.Empty; });
            }
        }

        private char OnValidateInput(string text, int charIndex, char addedChar)
        {
            // If we're at the character limit, show the warning and reject the character
            if (text.Length >= _maxMessageCharacterLimit)
            {
                ShowCharacterLimitExceededMessage();
                return '\0'; // Return null character to reject the input
            }

            return addedChar; // Accept the character
        }

        private void ShowCharacterLimitExceededMessage()
        {
            _overlayDialogConfig.DisplayType = DisplayType.FloatingText;
            _overlayDialogConfig.TargetTransform = transform;
            _overlayDialogConfig.ShowBackground = true;
            _overlayDialogConfig.TextToDisplay = TeamChatCharacterLimitExceeded;
            _overlayDialogConfig.TextArgs = new object[] { _maxMessageCharacterLimit };
            _overlayDialogManager.ShowOverlayDialog(_overlayDialogConfig);
        }

        private void MessageButtonHandler()
        {
            if (Application.isEditor)
            {
                _messageInputField.ActivateInputField();
                AnchorToTouchScreenKeyboard(DefaultEditorAnimationTime, _debugKeyboardHeightRatio);
            }
            else
            {
                _customKeyboard.ShowKeyboard();
            }

            _actionButtonsHolder.Enable(false);
        }

        private void AttachmentClickHandler(ChatMessage message)
        {
            _attachmentScreen.Setup(message);
            _attachmentMessageInteractions.Setup(message, _chatManager.IsLocalUser(message.Sender.Id),
                (reactionMessage, reactionUid) => { _reactionSelectedCallback.SafeInvoke(reactionMessage, reactionUid, () => _attachmentScreen.Hide()); },
                deletedMessage =>
                {
                    _attachmentScreen.Hide();
                    _deleteMessageCallback.SafeInvoke(deletedMessage);
                }, flaggedMessage =>
                {
                    _attachmentScreen.Hide();
                    _flagMessageCallback.SafeInvoke(flaggedMessage);
                });
        }

    }
}
