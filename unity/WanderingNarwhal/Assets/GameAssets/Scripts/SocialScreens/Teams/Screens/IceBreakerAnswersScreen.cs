using System;
using System.Collections.Generic;
using BBB;
using BBB.Chat;
using BBB.Core;
using BBB.DI;
using BBB.Social.Chat;
using BBB.UI.Core;
using GameAssets.Scripts.SocialScreens.Teams.IceBreaker;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat.MessageActions;
using GameAssets.Scripts.SocialScreens.Teams.TeamChat.Messages;
using GameAssets.Scripts.SocialScreens.Teams.Utils;
using RPC.Teams;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.UI;

namespace GameAssets.Scripts.SocialScreens.Teams.Screens
{
    public class IceBreakerAnswersScreen : ContextedUiBehaviour
    {
        public bool IsShown { get; private set; }
        private static readonly int HideTrigger = Animator.StringToHash("Hide");

        [SerializeField] private Animator _animator;
        [SerializeField] private Button _closeButton;

        [SerializeField] private RectTransform _listRoot;
        [SerializeField] private ScrollRect _scrollRect;

        [SerializeField] private GameObject _localMessageViewPrefab;
        [SerializeField] private GameObject _messageViewPrefab;
        [SerializeField] private int _canvasSortingOrder = 142;

        private IChatManager _chatManager;
        private ISocialManager _socialManager;

        private readonly Dictionary<string, IMessageView> _messageViewsCache = new();
        private Action<ChatMessage, string, Action> _reactionSelectedCallback;
        private Action<ChatMessage> _deleteMessageCallback;
        private Action<ChatMessage> _flagMessageCallback;
        private MessageContextDialog _messageContextDialog;
        private IceBreakerQuestion _iceBreakerQuestion;
        private List<ChatMessage> _cachedFilteredMessages;
        private Action _hideCallback;
        private bool _isMessageCacheDirty = true;
        private TeamData _currentTeam;

        private void Start()
        {
            _closeButton.ReplaceOnClick(HideWithAnimation);
        }

        protected override void InitWithContextInternal(IContext context)
        {
            _chatManager = context.Resolve<IChatManager>();
            _socialManager = context.Resolve<ISocialManager>();
        }

        public void Setup(MessageContextDialog messageContextDialog, TeamData currentTeam, IceBreakerQuestion iceBreakerQuestion,
            Action<ChatMessage, string, Action> reactionSelectedCallback, Action<ChatMessage> deleteMessageCallback,
            Action<ChatMessage> flagMessageCallback, Action hideCallback)
        {
            if (_iceBreakerQuestion?.Uid != iceBreakerQuestion.Uid || _currentTeam?.TeamUid != currentTeam.TeamUid)
            {
                InvalidateMessageCache();
            }

            _currentTeam = currentTeam;
            _messageContextDialog = messageContextDialog;
            _iceBreakerQuestion = iceBreakerQuestion;
            _reactionSelectedCallback = reactionSelectedCallback;
            _deleteMessageCallback = deleteMessageCallback;
            _flagMessageCallback = flagMessageCallback;
            _hideCallback = hideCallback;

            Clear();
        }

        private BaseMessageView FindBaseMessageViewById(string messageId)
        {
            if (_messageViewsCache.TryGetValue(messageId, out var messageView))
            {
                return messageView as BaseMessageView;
            }

            return null;
        }

        private List<ChatMessage> GetCachedFilteredMessages()
        {
            if (_isMessageCacheDirty || _cachedFilteredMessages == null)
            {
                RebuildMessageCache();
            }

            return _cachedFilteredMessages;
        }

        private void RebuildMessageCache()
        {
            if (_iceBreakerQuestion == null)
            {
                _cachedFilteredMessages = new List<ChatMessage>();
                _isMessageCacheDirty = false;
                return;
            }

            var allMessages = new List<ChatMessage>(_chatManager.Messages.Values);
            _cachedFilteredMessages = new List<ChatMessage>();

            foreach (var message in allMessages)
            {
                var messageType = message.GetMessageType();
                if (messageType != MessageType.IceBreakerAnswer)
                {
                    continue;
                }

                var iceBreakerQuestionUid =
                    message.AdditionalProperties.GetStringProperty(
                        ChatMessageAdditionalProperties.IceBreakerQuestionUid);

                if (iceBreakerQuestionUid == _iceBreakerQuestion.Uid)
                {
                    _cachedFilteredMessages.Add(message);
                }
            }

            _cachedFilteredMessages.Sort((message1, message2) => message2.CreatedAt.CompareTo(message1.CreatedAt));

            _isMessageCacheDirty = false;
        }

        private void InvalidateMessageCache()
        {
            _isMessageCacheDirty = true;
        }

        public void UpdateMessage(ChatMessage message)
        {
            if (!IsShown || message.GetMessageType() != MessageType.IceBreakerAnswer)
                return;

            var iceBreakerQuestionUid =
                message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.IceBreakerQuestionUid);

            if (iceBreakerQuestionUid == _iceBreakerQuestion?.Uid)
            {
                InvalidateMessageCache();
            }

            _messageContextDialog.UpdateMessage(message);

            var baseMessageView = FindBaseMessageViewById(message.Id);
            if (baseMessageView == null)
                return;

            baseMessageView.Setup(message, message.GetMessageType(), baseMessageView.IsHeadless, baseMessageView.IsOwn);
            baseMessageView.SetupReactions(message, true);

            LayoutRefreshRequested();
        }

        public void DeleteMessage(ChatMessage message)
        {
            if (!IsShown || message.GetMessageType() != MessageType.IceBreakerAnswer)
                return;

            var iceBreakerQuestionUid =
                message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.IceBreakerQuestionUid);

            if (iceBreakerQuestionUid == _iceBreakerQuestion?.Uid)
            {
                InvalidateMessageCache();
            }

            var baseMessageView = FindBaseMessageViewById(message.Id);
            if (baseMessageView == null)
                return;

            _messageViewsCache.Remove(message.Id);

            baseMessageView.HideAndDestroy();

            LayoutRefreshRequested();
        }

        public void AddMessage(ChatMessage message)
        {
            if (!IsShown || message.GetMessageType() != MessageType.IceBreakerAnswer)
                return;

            var iceBreakerQuestionUid =
                message.AdditionalProperties.GetStringProperty(ChatMessageAdditionalProperties.IceBreakerQuestionUid);

            if (iceBreakerQuestionUid != _iceBreakerQuestion?.Uid)
                return;

            InvalidateMessageCache();

            if (FindBaseMessageViewById(message.Id) != null)
            {
                return;
            }

            var isLocal = _chatManager.IsLocalUser(message.Sender.Id);
            var prefab = isLocal ? _localMessageViewPrefab : _messageViewPrefab;
            var messageType = message.GetMessageType();

            SpawnMessageView<BaseMessageView>(prefab, view =>
            {
                view.Setup(message, messageType, false, isLocal);
                view.SetupReactions(message);
                view.SetupClickCallback(MessageClickHandler);
                _messageViewsCache.Add(message.Id, view);
            }, true);

            LayoutRefreshRequested();
        }

        private void InternalShow()
        {
            var messagesList = GetCachedFilteredMessages();

            foreach (var message in messagesList)
            {
                var messageType = message.GetMessageType();
                var isLocal = _chatManager.IsLocalUser(message.Sender.Id);
                var prefab = isLocal ? _localMessageViewPrefab : _messageViewPrefab;

                SpawnMessageView<BaseMessageView>(prefab, view =>
                {
                    view.Setup(message, messageType, false, isLocal);
                    view.SetupReactions(message);
                    view.SetupClickCallback(MessageClickHandler);
                    _messageViewsCache.Add(message.Id, view);
                });
            }

            RefreshScrollRect();
            _socialManager.MarkAllIceBreakerAnswersAsRead();
        }

        private void SpawnMessageView<T>(GameObject prefab, Action<T> init, bool setAsFirstSibling = false) where T : Component
        {
            Profiler.BeginSample($"Instantiate[{prefab.name}]");
            var go = Instantiate(prefab, _listRoot);
            if (setAsFirstSibling)
            {
                go.transform.SetAsFirstSibling();
            }
            Profiler.EndSample();
            var view = go.GetComponent<T>();
            if (view == null)
            {
                BDebug.LogError(LogCat.General, $"Missing {typeof(T).Name} on prefab {go.name}");
                Destroy(go);
                return;
            }

            init(view);
            go.SetActive(true);
        }

        private void Clear()
        {
            _listRoot.RemoveAllActiveChilden();
            _messageContextDialog?.Hide();
            _messageViewsCache.Clear();
        }

        private void MessageClickHandler(BaseMessageView messageView)
        {
            var isLocal = _chatManager.IsLocalUser(messageView.Message.Sender.Id);

            if (!_messageContextDialog.IsShown)
            {
                _messageContextDialog.SetOverrideCanvasSortingOrder(_canvasSortingOrder);
                _messageContextDialog.Setup(messageView, isLocal, true,
                    (reactionMessage, reactionUid) =>
                    {
                        _reactionSelectedCallback.SafeInvoke(reactionMessage, reactionUid,
                            () => { _messageContextDialog.Hide(); });
                    }, _deleteMessageCallback,
                    _flagMessageCallback);
                _messageContextDialog.Show();
            }
            else
            {
                _messageContextDialog.Hide();
            }
        }

        private void RefreshScrollRect()
        {
            ForceRecursiveLayoutUpdater.UpdateLayout(_scrollRect.transform);
            _scrollRect.verticalNormalizedPosition = 0;
        }

        private void LayoutRefreshRequested()
        {
            var firstVisibleItem = GetFirstVisibleItem();
            var pixelOffset = firstVisibleItem != null ? GetItemPixelOffset(firstVisibleItem) : 0f;
            ForceRecursiveLayoutUpdater.UpdateLayout(_scrollRect.transform);
            if (firstVisibleItem != null)
            {
                RestoreScrollPositionByPixelOffset(firstVisibleItem, pixelOffset);
            }

            _messageContextDialog.RefreshPosition();
        }

        private RectTransform GetFirstVisibleItem()
        {
            var viewportBounds = GetViewportBounds();

            for (var i = 0; i < _listRoot.childCount; i++)
            {
                var child = _listRoot.GetChild(i) as RectTransform;
                if (child != null && child.gameObject.activeInHierarchy)
                {
                    var itemBounds = GetWorldBounds(child);
                    if (itemBounds.max.y >= viewportBounds.min.y)
                    {
                        return child;
                    }
                }
            }

            return null;
        }

        private float GetItemPixelOffset(RectTransform item)
        {
            var viewportBounds = GetViewportBounds();
            var itemBounds = GetWorldBounds(item);
            return viewportBounds.max.y - itemBounds.max.y;
        }

        private void RestoreScrollPositionByPixelOffset(RectTransform item, float targetOffset)
        {
            var viewportBounds = GetViewportBounds();
            var itemBounds = GetWorldBounds(item);
            var currentOffset = viewportBounds.max.y - itemBounds.max.y;
            var offsetDifference = currentOffset - targetOffset;

            var content = _scrollRect.content;
            var newPosition = content.anchoredPosition;
            newPosition.y += offsetDifference;
            content.anchoredPosition = newPosition;
        }

        private Bounds GetViewportBounds()
        {
            return RectTransformUtility.CalculateRelativeRectTransformBounds(_scrollRect.viewport);
        }

        private Bounds GetWorldBounds(RectTransform rectTransform)
        {
            return RectTransformUtility.CalculateRelativeRectTransformBounds(_scrollRect.viewport, rectTransform);
        }

        public void Show()
        {
            gameObject.SetActive(true);
            InternalShow();
            IsShown = true;
        }

        public void Hide()
        {
            Clear();
            gameObject.SetActive(false);
            IsShown = false;
        }

        public void HideWithAnimation()
        {
            _hideCallback?.Invoke();
            _animator.SetTrigger(HideTrigger);
        }
    }
}