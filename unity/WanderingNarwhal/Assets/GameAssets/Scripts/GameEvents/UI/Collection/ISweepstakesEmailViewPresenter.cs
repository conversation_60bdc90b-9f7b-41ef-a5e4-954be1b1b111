using System;
using BBB.UI.Core;
using UnityEngine;

namespace BBB
{
    public interface ISweepstakesEmailViewPresenter : IViewPresenter
    {
        event Action<string> SubmitEmailButtonPressedEvent;
        string EmailConfirmButtonId { get; }
        string EmailRejectButtonId { get; }
        string EmailConfirmTitleId { get; }
        string EmailConfirmMessageId { get; }
        void Setup(GameObject milestonePrefab, int milestoneNumber, string defaultEmail);
        void ShowInvalidInput<T>(SweepstakesInvalidInputType invalidInputType, T caller, Action<T, Transform, string> callback);
    }
}