using BBB.DI;
using FBConfig;

namespace BBB
{
    public class SweepstakesTermsOpener : WebPageOpener
    {
        protected override string URL => _termsUrl ?? base.URL;
        
        private string _termsUrl;

        protected override void OpenWebPage()
        {
            LazyInit();
            base.OpenWebPage();
        }

        protected override void InitWithContextInternal(IContext context)
        {
            var config = context.Resolve<IConfig>();
            var sweepstakesConfig = config.TryGetDefaultFromDictionary<SweepStakesGameEventConfig>();
            if (!sweepstakesConfig.IsNull())
            {
                _termsUrl = sweepstakesConfig.TermsUrl;
            }
        }
    }
}