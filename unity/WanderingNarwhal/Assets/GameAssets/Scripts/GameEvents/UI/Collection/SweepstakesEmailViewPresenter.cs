using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Generic.Modal;
using BBB.Screens;
using DG.Tweening;
using TMPro;
using UnityEngine;
using Button = UnityEngine.UI.Button;

namespace BBB
{
    public class SweepstakesEmailViewPresenter : ModalsViewPresenter, ISweepstakesEmailViewPresenter,
        IDestroyable
    {
        [SerializeField] private LocalizedTextPro _title; 
        [SerializeField] private Button _submitButton;
        [SerializeField] private TMP_InputField _emailInputField;
        [SerializeField] private Transform _bannerParent;
        [field:SerializeField] public string EmailConfirmButtonId { get; set; }
        [field:SerializeField] public string EmailRejectButtonId { get; set; }
        [field:SerializeField] public string EmailConfirmTitleId { get; set; }
        [field:SerializeField] public string EmailConfirmMessageId { get; set; }
        [SerializeField] private string[] _titleIds;
        [SerializeField] private string _invalidEmailId;
        [SerializeField] private string _noConnectionId;
        [SerializeField] private float _shakeDuration;
        [SerializeField] private float _shakeStrength;
        
        private ILocalizationManager _localizationManager;
        private readonly Dictionary<GameObject, GameObject> _milestoneBanners = new();
        private readonly Dictionary<SweepstakesInvalidInputType, string> _invalidInputIds = new();
        private GameObject _currentBanner;
        private Tweener _doShake;
        public event Action<string> SubmitEmailButtonPressedEvent;

        protected override void Awake()
        {
            base.Awake();
            _submitButton.ReplaceOnClick(SubmitButtonHandler);
        }

        public void Setup(GameObject milestonePrefab, int milestoneNumber, string defaultEmail)
        {
            _emailInputField.text = defaultEmail;
            _title.SetTextId(_titleIds[milestoneNumber]);
            if (_milestoneBanners.TryGetValue(milestonePrefab, out _currentBanner))
            {
                _currentBanner.SetActive(true);
            }
            else
            {
                _currentBanner = Instantiate(milestonePrefab, _bannerParent);
                _milestoneBanners.Add(milestonePrefab, _currentBanner);
            }

            if (_invalidInputIds.Count == 0)
            {
                _invalidInputIds.Add(SweepstakesInvalidInputType.InvalidEmail, _invalidEmailId);
                _invalidInputIds.Add(SweepstakesInvalidInputType.NoConnection, _noConnectionId);
            }
        }

        public void ShowInvalidInput<T>(SweepstakesInvalidInputType invalidInputType, T caller, Action<T, Transform, string> callback)
        {
            _doShake?.Kill(true);
            _doShake = _emailInputField.transform.DOShakePosition(_shakeDuration, Vector3.right * _shakeStrength);
            if (!_invalidInputIds.TryGetValue(invalidInputType, out var textId))
            {
                textId = _invalidEmailId;//default id
                BDebug.LogError(LogCat.Events, $"No text id is registered for input type {invalidInputType}");
            }
            callback.SafeInvoke(caller, _submitButton.transform, textId);
        }

        private void SubmitButtonHandler()
        {
            SubmitEmailButtonPressedEvent.SafeInvoke(_emailInputField.text);
        }

        protected override void OnHide()
        {
            base.OnHide();
            if (_currentBanner != null)
            {
                _currentBanner.SetActive(false);
            }
            
            Clear();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _milestoneBanners.Clear();
            _invalidInputIds.Clear();
            Clear();
        }

        private void Clear()
        {
            _doShake?.Kill(true);
            _doShake = null;
        }
    }
}