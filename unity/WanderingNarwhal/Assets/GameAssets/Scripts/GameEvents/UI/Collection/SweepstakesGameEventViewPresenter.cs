using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Screens;
using BebopBee.Core.Audio;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.GameEvents.UI.Collection;
using GameAssets.Scripts.UI.OverlayDialog;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Video;

namespace BBB
{
    public class SweepstakesGameEventViewPresenter : ModalsViewPresenter, ISweepstakesGameEventViewPresenter,
        IDestroyable
    {
        [SerializeField] private Button _infoButton;
        [SerializeField] private Button _termsButton;
        [SerializeField] private Button _playButton;
        [SerializeField] private Transform _startButtonFloatingTextAnchor;
        [SerializeField] private VideoPlayer _videoPlayer;
        [SerializeField] private SweepstakesProgressBarWidget _progressBar;
        [SerializeField] private Transform _rewardsContainer;
        [SerializeField] private SweepstakesRewardItem _rewardItemPrefab;
        [SerializeField, Range(0, 1)] private float _videoSoundVolume;
        [SerializeField] private GameObject _videoLoadingImage;

        public event Action<Transform> OkButtonPressedEvent;
        public event Action InfoButtonPressedEvent;
        public event Action TermsButtonPressedEvent;

        private ILocalizationManager _localizationManager;
        private readonly List<SweepstakesRewardItem> _rewardItems = new ();
        private IAssetsManager _assetsManager;
        
        private bool _hiddenAfterLoading;
        private IOverlayDialogManager _overlayDialogManager;

        protected override void OnContextInitialized(IContext context)
        {
            _localizationManager = context.Resolve<ILocalizationManager>();
            _assetsManager = context.Resolve<IAssetsManager>();
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();

            _infoButton.ReplaceOnClick(OnInfoButtonClicked);
            _playButton.ReplaceOnClick(OnPlayButtonPressed);
            
            //do not call ReplaceOnClick
            //to not break the internal flow of terms button
            _termsButton.AddOnClick(OnTermsButtonClicked);
        }

        private void OnInfoButtonClicked()
        {
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
            InfoButtonPressedEvent.SafeInvoke();
        }

        private void OnTermsButtonClicked()
        {
            TermsButtonPressedEvent.SafeInvoke();
        }

        private void OnPlayButtonPressed()
        {
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
            OkButtonPressedEvent.SafeInvoke(_startButtonFloatingTextAnchor);
        }

        public void Refresh(SweepstakesGameEventViewModel viewModel)
        {
            _hiddenAfterLoading = false;
            _videoLoadingImage.SetActive(true);
            viewModel.VideoClip.ContinueWith(asset =>
            {
                if (asset == null)
                {
                    BDebug.LogError(LogCat.Resources, "Sweestakes main event video not found.");
                    return;
                }
                if (_hiddenAfterLoading)
                {
                    _assetsManager.UnloadAsset(asset.Get());
                    return;
                }
                _videoPlayer.clip = asset.Get();
                _videoPlayer.prepareCompleted -= VideoPlayerPrepareCompletedHandler;
                _videoPlayer.prepareCompleted += VideoPlayerPrepareCompletedHandler;
                _videoPlayer.Prepare();
            });
            _progressBar.Refresh(viewModel.ProgressbarData, _localizationManager, _overlayDialogManager);
            
            SetupRewards(viewModel);
        }
        
        private void ClearRenderTexture(RenderTexture rt)
        {
            var prev = RenderTexture.active;
            RenderTexture.active = rt;
            GL.Clear(true, true, Color.black);
            RenderTexture.active = prev;
        }

        private void VideoPlayerPrepareCompletedHandler(VideoPlayer source)
        {
            _videoLoadingImage.SetActive(false);
        }

        private void SetupRewards(SweepstakesGameEventViewModel viewModel)
        {
            var totalMilestones = viewModel.Milestones.Count;
            for (int i = 0; i < totalMilestones; i++)
            {
                var milestoneIndex = totalMilestones - i - 1; //show milestones in reverse order
                if (i < _rewardsContainer.childCount)
                {
                    SweepstakesRewardItem rewardItem;
                    if (_rewardItems.Count <= i)
                    {
                        rewardItem = _rewardsContainer.GetChild(i).GetComponent<SweepstakesRewardItem>();
                        _rewardItems.Add(rewardItem);
                    }
                    else
                    {
                        rewardItem = _rewardItems[i];
                    }
                    rewardItem.Setup(i + 1, viewModel.Milestones[milestoneIndex].Sprite);
                }
                else
                {
                    var rewardItem = Instantiate(_rewardItemPrefab, _rewardsContainer);
                    rewardItem.Setup(i + 1, viewModel.Milestones[milestoneIndex].Sprite);
                    _rewardItems.Add(rewardItem);
                }
            }

            var toDestroy = new List<GameObject>();
            for (int i = _rewardsContainer.childCount - 1; i >= viewModel.Milestones.Count; i--)
            {
                toDestroy.Add(_rewardsContainer.GetChild(i).gameObject);
                if (_rewardItems.Count > i)
                {
                    _rewardItems.RemoveAt(i);
                }
            }

            foreach (var item in toDestroy)
            {
                Destroy(item);
            }
        }

        protected override void OnShow()
        {
            base.OnShow();

            _videoPlayer.Play();
            _videoPlayer.SetDirectAudioVolume(0, _videoSoundVolume);
            _progressBar.OnShow();
            AudioProxy.MuteMusic(true);
        }

        public override void Hide()
        {
            base.Hide();
            AudioProxy.MuteMusic(false);

            _videoPlayer.Stop();
            ClearRenderTexture(_videoPlayer.targetTexture);
            if (_videoPlayer.clip != null)
            {
                _assetsManager.UnloadAsset(_videoPlayer.clip);
            }
            _hiddenAfterLoading = true;
            _videoPlayer.clip = null;
            _progressBar.Clear();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            AudioProxy.MuteMusic(false);
        }
    }
}