using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.Generic.Modal;
using BBB.Screens;
using BBB.UI;
using BebopBee.Core.Audio;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameAssets.Scripts.UI.OverlayDialog;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace BBB
{
    public class DailyCollectGameEventViewPresenter : ModalsViewPresenter, IDailyCollectGameEventViewPresenter, IDestroyable
    {
        private const string TileCollectTextLocId = "GE_DAILYCOLLECT_TILECOLLECT";
        private const string BoostersCollectTextLocId = "GE_DAILYCOLLECT_BOOSTERCOLLECT";
        
        private const string DailyCollectRewardSingleLocId = "GE_DAILYCOLLECT_REWARD_SINGLE_{0}";
        private const string DailyCollectRewardLocId = "GE_DAILYCOLLECT_REWARD_{0}";
        private const string DailyCollectTargetTiles = "GE_DAILYCOLLECT_TARGET_TILES";
        private const string DailyCollectTargetGeneric = "GE_DAILYCOLLECT_TARGET_{0}S";
        private const string DailyCollectDetails = "GE_DAILYCOLLECT_DETAILS";
        private const string TileColorLoc = "TILE_COLOR_{0}";

        [Header("Progress bar")]
        [SerializeField] private ProgressBar _progressBar;
        [SerializeField] private Image _progressGoalImage;
        [SerializeField] private TextMeshProUGUI _progressText;
        [SerializeField] private Image _progressRewardImage;
        [SerializeField] private Image _progressRewardRibbon;
        [SerializeField] private TextMeshProUGUI _rewardAmountText;
        [SerializeField] private ClockCountdownText _clockCountdownText;
        [SerializeField] private GameObject _giftBoxImageHolder;
        [SerializeField] private Button _rewardButton;
        [FormerlySerializedAs("_speechBubbleConfig")] [SerializeField] private OverlayDialogConfig _overlayDialogConfig;

        [Header("Main area")]
        [SerializeField] private LocalizedTextPro _goalTextLocalized;
        [SerializeField] private Sprite _tileEventSprite;
        [SerializeField] private Sprite _boosterEventSprite;
        [SerializeField] private Image _eventImage;
        [SerializeField] private Button _playButton;
        [SerializeField] private Transform _startButtonFloatingTextAnchor;
        [SerializeField] private List<LocalizedTextPro> _nameTexts;
        [SerializeField] private GameObject[] _tileEventItems;
        [SerializeField] private GameObject[] _boosterEventItems;
        
        private IAssetsManager _assetsManager;
        private ILocalizationManager _localizationManager;
        private CurrencyIconsLoader _currencyIconsLoader;
        private DailyCollectGameEventViewModel _viewModel;
        private IOverlayDialogManager _overlayDialogManager;
        public event Action<Transform> OkButtonPressedEvent;
        
        protected override void OnContextInitialized(IContext context)
        {
            _localizationManager = context.Resolve<ILocalizationManager>();
            _assetsManager = context.Resolve<IAssetsManager>();
            _currencyIconsLoader = context.Resolve<CurrencyIconsLoader>();
            _overlayDialogManager = context.Resolve<IOverlayDialogManager>();
            _playButton.ReplaceOnClick(OnPlayButtonPressed);
        }

        public void Refresh(DailyCollectGameEventViewModel viewModel)
        {
            _viewModel = viewModel;
            var viewMode = viewModel.ViewMode;
            _playButton.gameObject.SetActive(viewMode == GameEventViewMode.Active || viewMode == GameEventViewMode.Failure);

            var milestoneTarget = viewModel.GameEvent.GetCurrentMilestoneTarget();
            var isColorParty = milestoneTarget.IsGoalColorTile();

            foreach (var nameText in _nameTexts)
            {
                nameText.SetTextId(isColorParty ? TileCollectTextLocId : BoostersCollectTextLocId);
            }

            var imageSprite = isColorParty ? _tileEventSprite : _boosterEventSprite;
            if (_eventImage != null && imageSprite != null)
            {
                _eventImage.sprite = imageSprite;
            }

            if (_tileEventItems is {Length: > 0})
            {
                foreach (var item in _tileEventItems)
                {
                    item.SetActive(isColorParty);
                }
            }

            if (_boosterEventItems is {Length: > 0})
            {
                foreach (var item in _boosterEventItems)
                {
                    item.SetActive(!isColorParty);
                }
            }
            
            var curMilestoneIndex = viewModel.GameEvent.GetCurrentMilestoneIndex();
            var mileStone = viewModel.GameEvent.GetMilestoneByIndex(curMilestoneIndex);
            if (mileStone == null)
            {
                return;
            }
            
            var rewardName = mileStone.RewardUid;
            if (rewardName.Contains("_inf"))
            {
                _rewardAmountText.text = viewModel.CurrentRewardAmount + " min";
            }
            else
            {
                _rewardAmountText.text = viewModel.CurrentRewardAmount.ToString();
            }

            _clockCountdownText.Init(_localizationManager, viewModel.TimeLeftGetter);

            RefreshProgressView(viewModel);
            RefreshEventImages(viewModel);
            RefreshGoalDescription(viewModel);
        }

        public override void OnShowAnimationFinished()
        {
            base.OnShowAnimationFinished();
            AnimateProgressView(_viewModel);
        }

        protected override void OnHide()
        {
            _clockCountdownText.Uninit();
            _progressBar.Clean();
            _viewModel = null;
            base.OnHide();
        }

        private void RefreshProgressView(DailyCollectGameEventViewModel viewModel)
        {
            var scoreToSet = viewModel.CurrentScore;
            var scoreGoal = viewModel.ScoreGoal;
            var progressToSet = (float)scoreToSet / scoreGoal;
            _progressBar.SetProgress(progressToSet);
            _progressText.text = viewModel.CurrentProgressText;
        }

        private void AnimateProgressView(DailyCollectGameEventViewModel viewModel)
        {
            var currentScore = Mathf.Max(0, viewModel.CurrentScore);
            var scoreGoal = viewModel.ScoreGoal;
            var progressToSet = (float)currentScore / scoreGoal;

            if (currentScore > 0)
            {
                _progressBar.AnimateProgressTo(progressToSet, 0.2f, Ease.Linear);
                _progressText.text = viewModel.CurrentProgressText;
                AudioProxy.PlaySound(GenericSoundIds.GameEventProgressBar);
            }
        }

        private void RefreshEventImages(DailyCollectGameEventViewModel viewModel)
        {
            var gameEvent = viewModel.GameEvent;
            var curMilestoneIndex = gameEvent.GetCurrentMilestoneIndex();
            var mileStone = gameEvent.GetMilestoneByIndex(curMilestoneIndex);
            if (mileStone == null)
            {
                return;
            }
            
            var showSpineGiftBox = mileStone.IsGiftBoxReward();
            _progressRewardRibbon.gameObject.SetActive(!showSpineGiftBox);
            _giftBoxImageHolder.SetActive(showSpineGiftBox);
            _progressRewardImage.gameObject.SetActive(!showSpineGiftBox);

            if (showSpineGiftBox)
            {
                _rewardButton.interactable = true;
                _rewardButton.ReplaceOnClick(() =>
                {
                    _overlayDialogConfig.DisplayType = DisplayType.RewardSpeechBubble;
                    _overlayDialogConfig.RewardToDisplay = mileStone.GiftBoxContent;
                    _overlayDialogManager.ToggleOverlayDialog(_overlayDialogConfig);
                });
            }
            else
            {
                _rewardButton.interactable = false;
            }

            _currencyIconsLoader.LoadAndGetCurrencySpriteAsync(mileStone.RewardSpriteName).ContinueWith(sprite => _progressRewardImage.sprite = sprite);
            _assetsManager.LoadSpriteAsync(mileStone.ScoreSpriteName)
                .ContinueWith(sprite => _progressGoalImage.sprite = sprite);
        }

        private void RefreshGoalDescription(DailyCollectGameEventViewModel viewModel)
        {
            var gameEvent = viewModel.GameEvent;

            var milestoneIndex = gameEvent.GetCurrentMilestoneIndex();
            var milestone = gameEvent.GetMilestoneByIndex(milestoneIndex);
            if (milestone == null)
            {
                return;
            }
            
            var scoreNameUpper = milestone.GoalName.ToUpper();

            string whatIsBeingCollected;
            if (gameEvent.GetCurrentMilestoneTarget().IsGoalColorTile())
            {
                var tileColorLoc = string.Format(TileColorLoc, scoreNameUpper);
                var tileColorName = _localizationManager.getLocalizedText(tileColorLoc);
                whatIsBeingCollected = _localizationManager.getLocalizedTextWithArgs(DailyCollectTargetTiles, tileColorName);
            }
            else
            {
                var targetLoc = string.Format(DailyCollectTargetGeneric, scoreNameUpper);
                whatIsBeingCollected = _localizationManager.getLocalizedText(targetLoc);
            }
            
            _goalTextLocalized.SetTextId(DailyCollectDetails, ColorUtility.ToHtmlStringRGB(Color.white).ToUpper(), viewModel.ScoreGoal, whatIsBeingCollected, GetRewardName());
            return;

            string GetRewardName()
            {
                var curMilestoneIndex = gameEvent.GetCurrentMilestoneIndex();
                var mileStone = gameEvent.GetMilestoneByIndex(curMilestoneIndex);
                if (mileStone == null)
                {
                    return string.Empty;
                }
                
                var amount = mileStone.RewardNumber;
                var rewardName = mileStone.RewardUid;
                var rewardLoc = DailyCollectRewardLocId;

                if (amount == 1 && CollectionGameEventExtensions.IsInventoryBooster(rewardName))
                {
                    rewardLoc = DailyCollectRewardSingleLocId;
                }
                
                var locKey = string.Format(rewardLoc, rewardName.ToUpper());
                return _localizationManager.getLocalizedTextWithArgs(locKey, amount);
            }
        }

        private void OnPlayButtonPressed()
        {
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
            OkButtonPressedEvent.SafeInvoke(_startButtonFloatingTextAnchor);
        }

        protected override void OnDestroy()
        {
            _progressGoalImage.sprite = null;
            _progressRewardImage.sprite = null;
            base.OnDestroy();
        }
    }
}