using System;
using BBB.Core;

namespace BBB
{
    public class SweepstakesEmailSubmittedModalController: EventBaseModalsController<ISweepstakesEmailSubmittedViewPresenter>
    {
        public event Action OnHideEvent;

        protected override void OnShow()
        {
            base.OnShow();
            Subscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.OkButtonClicked += OkButtonClickedHandler;
        }

        private void OkButtonClickedHandler()
        {
            OnCloseClicked();
        }

        private void Unsubscribe()
        {
            View.OkButtonClicked -= OkButtonClickedHandler;
        }

        protected override void OnHide()
        {
            base.OnHide();
            OnHideEvent?.Invoke();
            CLear();
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            CLear();
        }

        private void CLear()
        {
            Unsubscribe();
            OnHideEvent = null;
        }
    }
}