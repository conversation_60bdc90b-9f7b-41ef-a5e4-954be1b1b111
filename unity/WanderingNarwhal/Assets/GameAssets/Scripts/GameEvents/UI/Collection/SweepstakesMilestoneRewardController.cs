using System;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;
using UnityEngine;

namespace BBB.UI
{
    public class SweepstakesMilestoneRewardController : BaseModalsController<ISweepstakesMilestoneRewardViewPresenter>
    {
        private int _rewardIndex;
        public event Action OnModalHide;

        public override bool CanBypassTransition()
        {
            return true;
        }

        public void Setup(Transform rewardItem, int rewardIndex, Sprite rewardSprite, Action onHide)
        {
            _rewardIndex = rewardIndex;
            OnModalHide += onHide;
            DoWhenReady(() =>
            {
                View.Setup(rewardSprite, rewardItem, rewardIndex);
                Subscribe();
            });
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.OnTermsPressed += TermsButtonHandler;
        }

        private void Unsubscribe()
        {
            View.OnTermsPressed -= TermsButtonHandler;
        }

        private void TermsButtonHandler()
        {
            Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.Sweepstakes.Name, DauInteractions.Sweepstakes.Terms, $"{DauInteractions.Sweepstakes.SweepstakesRewardScreen}{_rewardIndex}"));
        }

        protected override void OnPostHide()
        {
            base.OnPostHide();
            OnModalHide.SafeInvoke();
            OnModalHide = null;
            Unsubscribe();
        }
    }
}
