using System.Collections.Generic;
using BBB.Core;
using BebopBee;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.GameEvents.UI.Collection;

namespace BBB
{
    public class SweepstakesMilestoneViewModel
    {
        public string EventUid { get; private set; }
        public int MilestoneNumber { get; private set; }
        public int BannerIndex { get; private set; }
        public float BannerStayDuration { get; private set; }
        public float BannerScrollDuration { get; private set; }
        public bool IsEmailProvided { get; private set; }
        public string DefaultEmail { get; private set; }
        public SweepstakesProgressbarWidgetData ProgressbarData { get; private set; }
        public List<SweepstakesVideoData> WinnersVideos { get; private set; }
        
        public static async UniTask<SweepstakesMilestoneViewModel> CreateViewModel(SweepStakesGameEventConfig sweepstakesConfig, HashSet<SweepstakesVideoConfig> videoConfigs, SweepstakesGameEvent gameEvent,
            IGameEventResourceManager resourceManager, IAccountManager accountManager, int bannerIndex)
        {
            var milestoneIndex = gameEvent.GetCurrentMilestoneIndex();
            var milestone = gameEvent.GetMilestoneByIndex(milestoneIndex);
            if (milestone.GetRelativeScore(gameEvent.CurrentScore) >= milestone.Goal)
            {
                milestoneIndex++;
            }
            
            var videoData = new List<SweepstakesVideoData>();
            foreach (var videoConfig in videoConfigs)
            {
                var thumbnail = await resourceManager.GetSpriteAsync(gameEvent.EventResourceId, videoConfig.ThumbnailName);

                if (thumbnail == null)
                {
                    BDebug.LogError(LogCat.Resources,$"Couldn't load video thumbnail '{videoConfig.ThumbnailName}' for event {gameEvent.EventResourceId}");
                }

                videoData.Add(new SweepstakesVideoData(videoConfig.Title, videoConfig.Url, thumbnail));
            }

            var progressbarData = await SweepstakesProgressbarWidgetData.CreateViewModelAsync(gameEvent, resourceManager);
            
            return new SweepstakesMilestoneViewModel
            {
                EventUid = gameEvent.EventResourceId,
                MilestoneNumber = milestoneIndex,
                BannerIndex = bannerIndex,
                BannerStayDuration = sweepstakesConfig.EligibilityBannersStayDuration,
                BannerScrollDuration = sweepstakesConfig.EligibilityBannersScrollDuration,
                IsEmailProvided = !accountManager.Profile.ProvidedEmail.IsNullOrEmpty(),
                DefaultEmail = accountManager.Profile.Email ?? string.Empty,
                ProgressbarData = progressbarData,
                WinnersVideos = videoData
            };
        }
    }
}