using BBB;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.GameEvents.UI.Collection
{
    public class SweepstakesRewardItem : BbbMonoBehaviour
    {
        [SerializeField] private Image _image;
        [SerializeField] private LocalizedTextPro _place;
        [SerializeField] private LocalizedTextPro _placeDescription;

        public void Setup(int place, Sprite rewardIcon)
        {
            _image.sprite = rewardIcon;
            _place.SetTextId($"SWEEPSTAKES_PRIZE_{place}");
            _placeDescription.SetTextId($"SWEEPSTAKES_PRIZE_INFO_{place}");
        }
    }
}