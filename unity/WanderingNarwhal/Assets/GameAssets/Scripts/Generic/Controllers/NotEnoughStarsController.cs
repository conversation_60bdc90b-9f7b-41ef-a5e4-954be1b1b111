using System;
using BBB;
using BBB.Core;
using BBB.DI;
using BBB.UI;
using GameAssets.Scripts.Generic.Views;

namespace GameAssets.Scripts.Generic.Controllers
{
    public class NotEnoughStarsController : BaseModalsController<INotEnoughStarsViewPresenter>
    {
        private IEventDispatcher _eventDispatcher;
        private Action _onClose;

        public void Setup(bool earnState = false, Action onClose = null)
        {
            _onClose = onClose;
            if (IsReady())
            {
                SetupView(earnState);
            }
            else
            {
                DoWhenReady(() => SetupView(earnState));
            }
        }

        private void SetupView(bool earnState)
        {
            View.ContinueButtonClicked -= OnContinueButtonClicked;
            View.ContinueButtonClicked += OnContinueButtonClicked;
            View.SetupTitle(earnState);
        }

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);
            _eventDispatcher = context.Resolve<IEventDispatcher>();
        }
        
        private void OnContinueButtonClicked()
        {
            HideModal();
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<LevelFlowRequestedEvent>());
        }

        protected override void OnCloseButtonClicked()
        {
            if (_onClose != null)
            {
                _onClose.Invoke();
                base.OnCloseButtonClicked();
            }
            else
            {
                OnContinueButtonClicked();
            }
        }

        protected override void OnHide()
        {
            base.OnHide();
            View.ContinueButtonClicked -= OnContinueButtonClicked;
        }
    }
}
