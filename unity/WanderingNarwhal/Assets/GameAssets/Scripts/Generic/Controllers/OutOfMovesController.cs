using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.UI.Gacha.Controllers;
using BBB.UI.Level.Views;
using BBB.UI.IAP.Controllers;
using BBB.Core.Wallet;
using BBB.Wallet;
using BBB.Audio;
using BBB.Core.Analytics;
using BebopBee.Core.Audio;
using BebopBee.Core.UI;
using Core.Configs;
using FBConfig;
using GameAssets.Scripts.Promotions.Banners;
using UnityEngine;

namespace BBB.UI.Level.Controllers
{
    public class OutOfMovesController : BaseModalsController<IOutOfMovesViewPresenter>
    {
        private static readonly Type[] RequiredConfigs =
        {
            typeof(OfferConfig)
        };

        private const int InventoryMovesAddCount = 5;
        private const int DefaultMovesOffered = 5;

        private int _movesOffered;
        private ILevel _level;
        private LevelOutcome _currentOutcome;
        private bool _skipBanner;
        private int _purchasePlus5Counter;

        private Camera _levelCamera;
        private OfferConfig _offerConfig;
        private Tuple<float, float> _gridVerticalPositions;
        private OutOfMovesViewContentMode _cachedContentMode;
        private OutOfMovesViewContentMode _currentContentMode;
        private OutOfMovesContentData _cachedContentData;

        private IEventDispatcher _eventDispatcher;
        private GachaManager _gachaManager;
        private IWalletManager _walletManager;
        private IUIWalletManager _uiWalletManager;
        private IPlayerManager _playerManager;
        private BannerManager _bannerManager;
        private IConfig _config;
        private LevelController _levelController;
        private GoalResourcesPanel _goalResourcesPanel;

        private IInventory Inventory => _playerManager.PlayerInventory;
        private IWalletTransactionController WalletTransactionController => _walletManager.TransactionController;

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _playerManager = context.Resolve<IPlayerManager>();
            _gachaManager = context.Resolve<GachaManager>();
            _walletManager = context.Resolve<IWalletManager>();

            _uiWalletManager = context.Resolve<IUIWalletManager>();
            _config = context.Resolve<IConfig>();
            _bannerManager = context.Resolve<BannerManager>();

            SetupOfferConfig(_config);
            Config.OnConfigUpdated -= SetupOfferConfig;
            Config.OnConfigUpdated += SetupOfferConfig;
            BDebug.Log(LogCat.Match3, "OnContextInitialized in OutOfMovesController");
        }

        private void SetupOfferConfig(IConfig config, HashSet<Type> updatedConfigs = null)
        {
            if (updatedConfigs != null && !updatedConfigs.Overlaps(RequiredConfigs))
                return;

            var configName = "turns_plus_5";
            if (_purchasePlus5Counter > 0)
                configName += $"_{(_purchasePlus5Counter + 1).ToString()}";

            _offerConfig = config.GetFromDictionary<OfferConfig>(configName);

            _movesOffered = _level != null ? EscalatingMovesSetup(_level.GetNumPlayed()) : DefaultMovesOffered;
        }

        private int EscalatingMovesSetup(int numOfTimesPlayed)
        {
            
            if (_offerConfig.EscalatingMovesFbLength == 0)
                return DefaultMovesOffered;

            for (var i = 0; i < _offerConfig.EscalatingMovesFbLength; i++)
            {
                var escalatingMove = _offerConfig.EscalatingMovesFb(i);
                if(!escalatingMove.HasValue)
                    continue;

                if (escalatingMove.Value.Key == numOfTimesPlayed)
                {
                    return escalatingMove.Value.Value;
                }
            }

            var closestLowerKey = int.MinValue;
            var closestLowerValue = int.MinValue;

            for (var i = 0; i < _offerConfig.EscalatingMovesFbLength; i++)
            {
                var escalatingMove = _offerConfig.EscalatingMovesFb(i);
                if(!escalatingMove.HasValue)
                    continue;

                var key = escalatingMove.Value.Key;
                var value = escalatingMove.Value.Value;
                if (key < numOfTimesPlayed && key > closestLowerKey)
                {
                    closestLowerKey = key;
                    closestLowerValue = value;
                }
            }

            if (closestLowerKey != int.MinValue)
                return closestLowerValue;

            var minKey = int.MaxValue;
            
            for (var i = 0; i < _offerConfig.EscalatingMovesFbLength; i++)
            {
                var escalatingMove = _offerConfig.EscalatingMovesFb(i);
                if(!escalatingMove.HasValue)
                    continue;
                
                if (escalatingMove.Value.Key < minKey)
                {
                    minKey = escalatingMove.Value.Value;
                }
            }
            
            for (var i = 0; i < _offerConfig.EscalatingMovesFbLength; i++)
            {
                var escalatingMove = _offerConfig.EscalatingMovesFb(i);
                if(!escalatingMove.HasValue)
                    continue;

                if (escalatingMove.Value.Key == minKey)
                {
                    return escalatingMove.Value.Value;
                }
            }

            return default;
        }

        protected override void OnShow()
        {
            // in on show because of banners
            PurchasePath.Start(PurchaseStep.OutOfMoves);
            base.OnShow();
            SetupOfferConfig(_config);
            View.SetupOfferConfig(_offerConfig, DefaultMovesOffered, _movesOffered);
            View.UpdateContent(_currentContentMode, _cachedContentData, true);

            View.SetupSkipBanner(_skipBanner, _level);
            View.SetupGridVerticalPositions(_gridVerticalPositions, _levelCamera, _goalResourcesPanel);
            _bannerManager.SetupIAPPurchaseCallback(() =>
            {
                SetVisibleCoinsWidget(false);
                Hide();
            }, _ => { ShowModal(ShowMode.Delayed); });
            Subscribe();
            CheckForFreeSpin();
        }

        public void SetupLevel(ILevel level, LevelController levelController)
        {
            _level = level;
            _levelController = levelController;
            _cachedContentData.LevelUid = _level.LevelUid;
        }

        public void SetupGridPosition(Tuple<float, float> gridVerticalPositions, Camera levelCamera, GoalResourcesPanel goalResourcesPanel)
        {
            _gridVerticalPositions = gridVerticalPositions;
            _levelCamera = levelCamera;
            _goalResourcesPanel = goalResourcesPanel;
        }

        public void Setup(OutOfMovesContentData outOfMovesContentData)
        {
            _cachedContentData = outOfMovesContentData;
            _skipBanner = outOfMovesContentData.SkipBanner;
            _purchasePlus5Counter = outOfMovesContentData.PurchasePlus5Counter;
            _currentContentMode = outOfMovesContentData.CurrentOutcome == LevelOutcome.ShuffleFailed
                ? OutOfMovesViewContentMode.ShuffleLost
                : OutOfMovesViewContentMode.Gains;
            _currentOutcome = outOfMovesContentData.CurrentOutcome;
        }

        private void CheckForFreeSpin()
        {
            var cooldown = _gachaManager.GetTimeLeftUntilFreeRoll(GachaType.OutOfMoves);
            View.SetupFreeMove(cooldown <= 0d);
        }

        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
        }

        private void Subscribe()
        {
            Unsubscribe();
            View.TriedToPurchase += TryToPurchaseMoves;
            View.OpenedGacha += ShowGacha;
            View.TriedToUseInventory += SpendFromInventory;
            View.ShowAnimCompleted += OnShowAnimCompleted;
            View.ShowMatch3Board += OnShowMatch3Board;
            View.HideMatch3Board += OnHideMatch3Board;
        }

        private void Unsubscribe()
        {
            View.TriedToPurchase -= TryToPurchaseMoves;
            View.OpenedGacha -= ShowGacha;
            View.TriedToUseInventory -= SpendFromInventory;
            View.ShowAnimCompleted -= OnShowAnimCompleted;
            View.ShowMatch3Board -= OnShowMatch3Board;
            View.HideMatch3Board -= OnHideMatch3Board;
        }

        private void SpendFromInventory(string uid)
        {
            if (Inventory.HasItem(InventoryItems.ExtraMoves))
            {
                Inventory.SpendItem(InventoryItems.ExtraMoves, 1);
                var moreMovesAddedEvent = _eventDispatcher.GetMessage<MoreMovesAddedEvent>();
                moreMovesAddedEvent.Set(InventoryMovesAddCount, _offerConfig.ExtraReward, default, false);
                _eventDispatcher.TriggerEvent(moreMovesAddedEvent);
            }

            Hide();
            SetVisibleCoinsWidget(false);
        }

        private void ShowGacha()
        {
            SetVisibleCoinsWidget(false);
            Hide();
            var ctrl = ModalsBuilder.CreateModalView<OutOfMovesGachaController>(ModalsType.OutOfMovesGacha);
            ctrl.SetupOnClosePrizeReceivedAction(CheckForPrizeType);
            ctrl.SetupLevel(_level);
            ctrl.ShowModal();
            _skipBanner = true;
        }

        private void CheckForPrizeType(Prize lastPrize)
        {
            if (lastPrize is { Type: GachaPrizeType.Moves })
            {
                var moreMovesAddedEvent = _eventDispatcher.GetMessage<MoreMovesAddedEvent>();
                moreMovesAddedEvent.Set(lastPrize.Value, null, default, false);
                _eventDispatcher.TriggerEvent(moreMovesAddedEvent);
                var movesWonEvent = _eventDispatcher.GetMessage<MovesWonEvent>();
                movesWonEvent.Set(lastPrize.Value);
                _eventDispatcher.TriggerEvent(movesWonEvent);
            }
            else
            {
                ShowModal(ShowMode.Delayed);
            }
        }

        private void SetContentMode(OutOfMovesViewContentMode contentMode, bool skipOutro = false)
        {
            _currentContentMode = contentMode;
            SetVisibleCoinsWidget(true);

            View.UpdateContent(contentMode, _cachedContentData, skipOutro);
        }

        private bool IsAnySecondModalEventScoreLoss()
        {
            return _cachedContentData.IsButlerStreakBroken
                   || _cachedContentData.RoyaleEventStreakLoss
                   || _cachedContentData.GameEventLoss
                   || _cachedContentData.SdbLost
                   || _cachedContentData.SweepstakesEventLoss;
        }

        private bool IsAnyThirdModalEventScoreLoss()
        {
            return _cachedContentData.CompetitionEventStreakLoss
                   || _cachedContentData.DiscoRushEventLoss;
        }

        protected override void OnCloseButtonClicked()
        {
            _skipBanner = true;
            switch (_currentContentMode)
            {
                case OutOfMovesViewContentMode.Gains:
                    if (IsAnySecondModalEventScoreLoss())
                    {
                        SetContentMode(OutOfMovesViewContentMode.SecondModal);
                        PlayClickSound();
                        return;
                    }

                    if (IsAnyThirdModalEventScoreLoss())
                    {
                        SetContentMode(OutOfMovesViewContentMode.ThirdModal);
                        PlayClickSound();
                        return;
                    }

                    break;

                case OutOfMovesViewContentMode.SecondModal:
                    if (IsAnyThirdModalEventScoreLoss())
                    {
                        SetContentMode(OutOfMovesViewContentMode.ThirdModal);
                        PlayClickSound();
                        return;
                    }

                    break;

                case OutOfMovesViewContentMode.ThirdModal:
                    // Go to the end of the method, where this modal is completely close
                    break;
            }

            base.OnCloseButtonClicked();
            SetupOfferConfig(_config);
            SetVisibleCoinsWidget(false);
            _levelController.ShowBoosterOfferModalOrExit(_currentOutcome);
        }

        private void OnShowMatch3Board()
        {
            PlayClickSound();
            _cachedContentMode = _currentContentMode;
            SetContentMode(OutOfMovesViewContentMode.ShowMatch3Board);
        }

        private void OnHideMatch3Board()
        {
            SetContentMode(_cachedContentMode);
        }

        protected override void PlayOpenningSound()
        {
            AudioProxy.PlaySound(GenericSoundIds.LevelFailPopupAppearing);
        }

        private static void PlayClickSound()
        {
            AudioProxy.PlaySound(GenericSoundIds.GenericButtonTap);
        }

        private void TryToPurchaseMoves()
        {
            var price = _offerConfig.PriceFb;
            if(!price.HasValue)
                return;

            var spentDictionary = new Dictionary<string, long>();

            for (var i = 0; i < price.Value.CurrenciesLength; i++)
            {
                var currency = price.Value.Currencies(i);
                if(!currency.HasValue)
                    continue;

                spentDictionary[currency.Value.Key] = currency.Value.Value;
            }
            var transaction = new Transaction()
                .Spend(spentDictionary)
                .SetAnalyticsData(CurrencyFlow.IGP.Name, CurrencyFlow.IGP.Moves, _playerManager.CurrentLevel.LevelUid)
                .AddTag(TransactionTag.OutOfMoves);

            var currentScreen = View.GetCurrentScreenInfoForDauAnalytics();

            if (WalletTransactionController.TryToMakeTransaction(transaction))
            {
                Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.OutOfMoves.CategoryName, currentScreen, DauInteractions.OutOfMoves.ExtraMovesCoins));
                _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.OutOfMoves);
                var moreMovesAddedEvent = _eventDispatcher.GetMessage<MoreMovesAddedEvent>();
                moreMovesAddedEvent.Set(_movesOffered, _offerConfig.ExtraReward, price.Value, true);
                _eventDispatcher.TriggerEvent(moreMovesAddedEvent);
                SetVisibleCoinsWidget(false, View.DelayBeforeWalletHide);
                Hide();
                _skipBanner = true;
            }
            else
            {
                Analytics.LogEvent(new DauInteractionsEvent(DauInteractions.OutOfMoves.CategoryName, currentScreen, DauInteractions.OutOfMoves.Store));
                ShowStore();
            }
        }

        private void ShowStore()
        {
            // after return  from shop, we start sequence of content showing from the beginning
            _currentContentMode = OutOfMovesViewContentMode.Gains;

            var storeController = ModalsBuilder.CreateModalView<StoreModalController>(ModalsType.Store);
            storeController.SetupOnCloseAction(() => ShowModal(ShowMode.Delayed), true);
            storeController.Show();
            SetVisibleCoinsWidget(false);
            Hide();
            _skipBanner = true;
        }

        private void OnShowAnimCompleted()
        {
            if (_currentContentMode != OutOfMovesViewContentMode.ShowMatch3Board)
            {
                SetVisibleCoinsWidget(true);
                // this triggers wallet reappearing when it might be hidden by gacha
                SetVisibleCoinsWidget(true, 1.0f);
            }
        }

        private void SetVisibleCoinsWidget(bool visible, float delay = 0f)
        {
            var changeTemporaryVisibilityEvent = _eventDispatcher.GetMessage<TopBarController.ChangeTemporaryVisibilityEvent>();
            changeTemporaryVisibilityEvent.Widgets = TopBarController.Widgets.Coins;
            changeTemporaryVisibilityEvent.Visible = visible;
            changeTemporaryVisibilityEvent.Delay = delay;
            _eventDispatcher.TriggerEvent(changeTemporaryVisibilityEvent);
        }

        public override void DisposeContext()
        {
            base.DisposeContext();
            Config.OnConfigUpdated -= SetupOfferConfig;
        }
    }
}