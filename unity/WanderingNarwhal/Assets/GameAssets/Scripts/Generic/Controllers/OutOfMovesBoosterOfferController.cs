using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Core.Analytics;
using BBB.DI;
using BBB.UI.Level.Views;
using BBB.UI.IAP.Controllers;
using BBB.Core.Wallet;
using BBB.GameAssets.Scripts.Player;
using BBB.Wallet;
using BebopBee;
using BebopBee.Core.UI;
using FBConfig;

namespace BBB.UI.Level.Controllers
{
    public class OutOfMovesBoosterOfferController : BaseModalsController<IOutOfMovesBoosterOfferViewPresenter>
    {
        private const float ShowCoinsWidgetTime = 4.5f;
        private const float DelayBeforeShowCoinsWidget = 2.0f;

        private IWalletManager _walletManager;
        private IWalletTransactionController _walletTransactionController => _walletManager.TransactionController;
        private IUIWalletManager _uiWalletManager;
        private IConfig _config;
        private IEventDispatcher _eventDispatcher;
        private Action _onPurchase;
        private Action _onCancel;
        private int _extraMoves;
        private IAPStoreVirtualItemPackConfig _virtualItemPacksConfig;
        private IAPStoreVirtualItemPackDescription _virtualItemPack;
        private IPlayerManager _playerManager;
        private IAccountManager _accountManager;
        private Stage _paletteStage;
        private string _purchasePath;

        protected override void OnInitializeByContext(IContext context)
        {
            base.OnInitializeByContext(context);

            _config = context.Resolve<IConfig>();
            _playerManager = context.Resolve<IPlayerManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _accountManager = context.Resolve<IAccountManager>();

            _walletManager = context.Resolve<IWalletManager>();
            _uiWalletManager = context.Resolve<IUIWalletManager>();
        }

        public void SetupBooster(string boosterUid, Action onPurchase, Action onCancel, Stage paletteStage, int extraMoves, string purchasePath)
        {
            _onPurchase = onPurchase;
            _onCancel = onCancel;
            _extraMoves = extraMoves;
            _paletteStage = paletteStage;
            _purchasePath = purchasePath;

            FindBoosterPack(boosterUid);
        }

        protected override void OnShow()
        {
            base.OnShow();

            View.Setup(_virtualItemPacksConfig, _virtualItemPack, _paletteStage, TryToPurchase);
            View.SetupAdditionalMoves(_extraMoves);

            Subscribe();

            SetVisibleCoinsWidget(true);
            // When this modal is shown after IAP Store + Gacha + Claim,
            // then those popups may hide coin widget after their transactions.
            // It's hardly possible to pass callback through 3-4 popups under different circumstances,
            // So the simplest solution is to re-trigger its showing again with some delay
            SetVisibleCoinsWidget(true, DelayBeforeShowCoinsWidget);
        }

        protected override void OnHide()
        {
            base.OnHide();
            Unsubscribe();
        }

        private void SetVisibleCoinsWidget(bool visible, float delay = 0f)
        {
            var changeTemporaryVisibilityEvent = _eventDispatcher.GetMessage<TopBarController.ChangeTemporaryVisibilityEvent>();
            changeTemporaryVisibilityEvent.Widgets = TopBarController.Widgets.Coins;
            changeTemporaryVisibilityEvent.Visible = visible;
            changeTemporaryVisibilityEvent.Delay = delay;
            _eventDispatcher.TriggerEvent(changeTemporaryVisibilityEvent);
        }

        private void Subscribe()
        {
            Unsubscribe();

            View.OnCloseClicked += OnCloseClickedHandler;
        }

        private void Unsubscribe()
        {
            View.OnCloseClicked -= OnCloseClickedHandler;
        }

        private void OnCloseClickedHandler()
        {
            SetVisibleCoinsWidget(false);
            _onCancel?.Invoke();
        }

        protected override void PlayOpenningSound()
        {
        }

        private void TryToPurchase()
        {
            var reward = new Dictionary<string, long>()
            {
                { _virtualItemPacksConfig.ItemUid, _virtualItemPack.Amount }
            };
            var price = _virtualItemPack.Price;
            
            if(!price.HasValue)
                return;
            
            var spendDictionary = new Dictionary<string, long>();
            for (var i = 0; i < price.Value.CurrenciesLength; i++)
            {
                var currency = price.Value.Currencies(i);
                
                if(!currency.HasValue)
                    continue;

                spendDictionary[currency.Value.Key] = currency.Value.Value;
            }
            

            var transaction = new Transaction()
                .Spend(spendDictionary)
                .Earn(reward)
                .SetAnalyticsData(CurrencyFlow.IGP.Name, _virtualItemPacksConfig.ItemUid + (_extraMoves > 0 ? CurrencyFlow.IGP.ExtraMoves : ""),
                    _playerManager.CurrentLevel != null ? _playerManager.CurrentLevel.LevelUid : _accountManager.Profile.HighestPassedLevelId)
                .AddTag(TransactionTag.OutOfMoves);

            if (_walletTransactionController.TryToMakeTransaction(transaction))
            {
                _onPurchase?.Invoke();
                _uiWalletManager.VisualizeAllTransactionsWithTag(TransactionTag.OutOfMoves);
                SetVisibleCoinsWidget(false, ShowCoinsWidgetTime);
                Hide();
            }
            else
            {
                ShowStore();
            }
        }

        private void ShowStore()
        {
            var storeController = ModalsBuilder.CreateModalView<StoreModalController>(ModalsType.Store);
            storeController.SetupOnCloseAction(() => ShowModal(ShowMode.Delayed), true);
            
            PurchasePath.Start(_purchasePath);
            PurchasePath.Append(_virtualItemPacksConfig.ItemUid + (_extraMoves > 0 ? CurrencyFlow.IGP.ExtraMoves : string.Empty));
            storeController.Show();

            SetVisibleCoinsWidget(false, ShowCoinsWidgetTime);
            Hide();
        }

        private void FindBoosterPack(string boosterUid)
        {
            _virtualItemPacksConfig = default;
            _virtualItemPack = default;
            var found = false;

            var virtualItemPackConfig = _config.Get<IAPStoreVirtualItemPackConfig>();
            foreach (var kvp in virtualItemPackConfig)
            {
                var virtualItemPack = kvp.Value;
                if (virtualItemPack.ItemUid == boosterUid)
                {
                    _virtualItemPacksConfig = virtualItemPack;
                    found = true;
                    break;
                }
            }

            if (!found)
            {
                UnityEngine.Debug.LogError("Missing config for: " + boosterUid);
                return;
            }

            var minAmount = int.MaxValue;
            for (var i = 0; i < _virtualItemPacksConfig.PacksFbLength; i++)
            {
                var iapStoreVirtualItemPackDescription = _virtualItemPacksConfig.PacksFb(i);
                
                if(!iapStoreVirtualItemPackDescription.HasValue || minAmount <= iapStoreVirtualItemPackDescription.Value.Amount)
                    continue;

                minAmount = iapStoreVirtualItemPackDescription.Value.Amount;
                _virtualItemPack = iapStoreVirtualItemPackDescription.Value;
            }
        }
    }
}