using DG.Tweening;
using UnityEngine;

namespace BBB.UI.Level
{
    [CreateAssetMenu(fileName = "GoalFlyAnimSettings", menuName = "BBB/M3/Goal Fly Settings [OLD]", order = 1)]
    public class GoalFlyAnimationSettings : ScriptableObject
    {
        public float StepsDelay = 0.1f;
        public float Duration = 0.9f;
        public float MinOffset = 0f;
        public float MaxOffset = 0.2f;
        public Ease EaseType = Ease.OutQuad;
        public Vector2 StartOffset;
        public float StartSoundDelay;
        public string StartSoundUid;
    }
}