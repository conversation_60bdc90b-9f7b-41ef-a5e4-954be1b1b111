using System;
using System.Collections.Generic;
using BBB.Audio;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.EndGameEvents;
using BBB.Match3.Renderer;
using BebopBee.Core.Audio;
using Beebopbee.Core.Extensions;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace BBB.UI.Level
{
    public class GoalResourcesPanelGameEventHandler : BbbMonoBehaviour
    {
        private GenericResourceProvider _genericResourceProvider;
        private RendererContainers _rendererContainers;
        private IGridController _gridController;
        
        private GameEventBase _currentGameEvent;

        private readonly Dictionary<string, Sprite> _animationGameEventSpriteMap = new Dictionary<string, Sprite>();

        public void Init(IContext context)
        {
            _genericResourceProvider = context.Resolve<GenericResourceProvider>();
            _rendererContainers = context.Resolve<RendererContainers>();
            _gridController = context.Resolve<IGridController>();
            var gameEventManagersCollection = context.Resolve<GameEventMatch3ManagersCollection>();

            foreach (var manager in gameEventManagersCollection)
            {
                var activeEvent = manager.ActiveGameEvent;
                if (activeEvent is { Status: GameEventStatus.Active} and not EndOfContentGameEvent)
                {
                    LoadSprite(activeEvent);
                }
            }
        }
        
        private void LoadSprite(GameEventBase gameEvent)
        {
            _genericResourceProvider.CacheAndLoadAsync<Sprite>(this, GetIconName(gameEvent))
                .ContinueWith(sprite =>
                {
                    _animationGameEventSpriteMap[gameEvent.Uid] = sprite;
                });
        }

        private string GetIconName(GameEventBase gameEvent)
        {
            if (gameEvent is CollectionGameEvent collectionGameEvent)
            {
                var iconName = $"{collectionGameEvent.EventResourceId}_{collectionGameEvent.GetScoreIconSpriteNameForCurrentMilestone()}";
                return iconName;
            }

            var result = gameEvent.Uid.RemoveDigits();
            result = result.RemoveVersion();
            return result + "_" + GameEventResKeys.ScoreIcon;
        }

        public void OnGameEventScoreCollected(string gameEventUid, Coords position, int count)
        {
            var sprite = _animationGameEventSpriteMap.GetValueOrDefault(gameEventUid);
            if (sprite == null || sprite == default) return;
            var tileGo = _rendererContainers.SpawnFx<CollectEventTileAmountFleeting>(FxType.CollectEventTile);
            tileGo.transform.localPosition = _gridController.ToLocalPosition(position);
            tileGo.Setup(sprite, count);
            AudioProxy.PlaySound(Match3SoundIds.TileKindLanding);
        }
    }
}