using System.Collections;
using System.Collections.Generic;
using BBB.Core.UI;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace BBB.UI.Level
{
    public class GoalResourceItem : BbbMonoBehaviour, IGoalUiElement
    {
        [SerializeField] private Image _icon;
        [SerializeField] private TextMeshProUGUI _amount;
        [SerializeField] private GameObject _checkItem;
        [SerializeField] private ParticleSystem _hitParticleSystem;
        [SerializeField] private Transform _highlightHolder;
        [SerializeField] private float _shineTime = 0.2f;
        [Space]
        [SerializeField] private float _onCollectFeedbackDuration = 0.416f;
        [SerializeField] private Transform _onCollectFeedbackAnimatedAnchor;
        [SerializeField] private AnimationCurve _onCollectFeedbackScaleX;
        [SerializeField] private AnimationCurve _onCollectFeedbackScaleY;

        private Vector3 _defaultScale;
        private int _count;
        private readonly List<GameObject> _highlightGos = new();

        private Coroutine _collectFeedbackRoutine;

        public GoalType GoalType
        {
            get => (GoalType)_goalType;
            private set => _goalType = (long)value;
        }

        private long _goalType;

        public bool IsCompleted => _count <= 0;

        private void Awake()
        {
            _defaultScale = _onCollectFeedbackAnimatedAnchor.localScale;
        }

        public void Init(GoalViewData goalViewData)
        {
            GoalType = goalViewData.Type;

            var sprite = goalViewData.Sprite;
            _icon.gameObject.SetActive(sprite != null);
            _icon.sprite = sprite;
            _count = goalViewData.TotalCount;
            DisplayAmount();
        }

        public void Init(Sprite sprite)
        {
            _icon.sprite = sprite;
            _count = 0;
            DisplayAmount();
        }

        public Image GetIcon()
        {
            return _icon;
        }

        public Sprite GetSprite()
        {
            return _icon == null ? null : _icon.sprite;
        }

        public void PlayHit()
        {
            if (!gameObject.activeInHierarchy) return;
            PlayParticleAnimation();
            _collectFeedbackRoutine ??= StartCoroutine(CollectFeedbackAnimationRoutine());
        }

        private IEnumerator CollectFeedbackAnimationRoutine()
        {
            float timer = 0f;
            while (timer < _onCollectFeedbackDuration)
            {
                float ratio = timer / _onCollectFeedbackDuration;
                float x = _onCollectFeedbackScaleX.Evaluate(ratio) * _defaultScale.x;
                float y = _onCollectFeedbackScaleY.Evaluate(ratio) * _defaultScale.y;
                timer += Time.deltaTime;
                _onCollectFeedbackAnimatedAnchor.localScale = new Vector3(x, y, _defaultScale.z);
                yield return null;
            }

            _onCollectFeedbackAnimatedAnchor.localScale = _defaultScale;
            _collectFeedbackRoutine = null;
        }

        public void DecreaseCounter(bool enableCheckMarkOnZero)
        {
            ChangeCounter(-1, enableCheckMarkOnZero);
        }

        public void ChangeCounter(int delta, bool enableCheckMarkOnZero = false)
        {
            _count = Mathf.Max(0, _count + delta);

            if (_count == 0 && !enableCheckMarkOnZero)
            {
                ShowCheckmark();
            }
            else
            {
                DisplayAmount();
            }
        }

        private void DisplayAmount()
        {
            _amount.text = _count.ToString();
            if (_checkItem != null)
            {
                _checkItem.SetActive(false);
            }
        }

        public int SetValueToZero()
        {
            var delta = _count;

            _count = 0;
            ShowCheckmark();

            return delta;
        }

        private void ShowCheckmark()
        {
            _amount.text = "";
            _checkItem.SetActive(true);
        }
        
        public void Highlight(IEnumerable<GameObject> highlightPrefabs)
        {
            if (_highlightGos.Count > 0)
            {
                foreach(var go in _highlightGos)
                    go.SetActive(true);
            }
            else
            {
                if (_highlightHolder != null)
                {
                    foreach (var prefab in highlightPrefabs)
                    {
                        UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{prefab.name}]");
                        var go = Instantiate(prefab, _highlightHolder, false);
                        UnityEngine.Profiling.Profiler.EndSample();
                        _highlightGos.Add(go);

                        var shineEffectController = go.GetComponent<ShineEffectController>();
                        if (shineEffectController)
                        {
                            var image = _highlightHolder.GetComponent<Image>();
                            if (image != null)
                            {
                                shineEffectController.SetupSprite(image.sprite);
                            }
                            shineEffectController.SetupShineTime(_shineTime);
                            shineEffectController.ApplyOffsetValues(-1f, 1f);
                        }
                    }
                }
            }
        }

        public void StopHighlight()
        {
            foreach(var go in _highlightGos)
                go.SetActive(false);
        }

        public void PlayParticleAnimation()
        {
            if (!_hitParticleSystem) return;
            _hitParticleSystem.Clear();
            _hitParticleSystem.Play(false);
        }
    }
}
