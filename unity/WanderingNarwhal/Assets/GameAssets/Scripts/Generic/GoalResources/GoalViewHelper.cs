using System.Collections.Generic;
using BBB.DI;
using BBB.GameAssets.UI.Level.Scripts.Views.Panels;
using BBB.Match3.Systems.GoalsService;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.UI.Level
{
    public class GoalViewData
    {
        public GoalType Type;
        public Sprite Sprite;
        public Color Color = Color.white;
        public int TotalCount;
        public int LeftCount;

        public ItemListPanelViewData ToItemListViewData()
        {
            return new ItemListPanelViewData
            {
                Sprite = Sprite,
                TotalCount = TotalCount,
                LeftCount = LeftCount
            };
        }
    }
    
    public class GoalViewHelper : IContextInitializable
    {
        private TilesResources _tilesResources;
        private TileResourceSelector _tileResourceSelector;
        private GoalsSystem _goalsSystem;
        
        public void InitializeByContext(IContext context)
        {
            _tilesResources = context.Resolve<TilesResources>();
            _tileResourceSelector = context.Resolve<TileResourceSelector>();
            _goalsSystem = context.Resolve<GoalsSystem>();
        }

        public async UniTask<List<GoalViewData>> GetGoalsViewDataAsync()
        {
            var result = new List<GoalViewData>();

            var tileKindTypes = GoalTypeExtensions.TileKindTypes();

            for (var i = tileKindTypes.Length - 1; i >= 0; i--)
            {
                var tileKind = tileKindTypes[i];
                var count = _goalsSystem.GetOriginalGoalCount(tileKind);
                
                if (count <= 0) 
                    continue;
                
                var tileData = await _tilesResources.GetAsync(tileKind.ToTileKind());

                result.Add(new GoalViewData
                {
                    Type = tileKind,
                    Sprite = tileData.Sprite,
                    Color = tileData.Color,
                    TotalCount = count,
                    LeftCount = _goalsSystem.GetLeftGoalCount(tileKind)
                });
            }

            var gridBasedTypes = GoalTypeExtensions.GridBasedTypes();

            for (var i = gridBasedTypes.Length - 1; i >= 0; i--)
            {
                var count = _goalsSystem.GetOriginalGoalCount(gridBasedTypes[i]);
                var goalType = gridBasedTypes[i];
                if (count > 0)
                { 
                    var sprite = _tileResourceSelector.GetGoalSprite(goalType);

                    result.Add(new GoalViewData
                    {
                        Type = goalType,
                        Sprite = sprite,
                        TotalCount = count,
                        LeftCount = _goalsSystem.GetLeftGoalCount(goalType)
                    });
                }
                else
                {
                    if (gridBasedTypes[i] == GoalType.Backgrounds && _goalsSystem.GetOriginalGoalCount(GoalType.Bush) > 0 
                        || gridBasedTypes[i] == GoalType.Petal  && _goalsSystem.GetOriginalGoalCount(GoalType.FlowerPot) > 0)
                    {
                        result.Add(new GoalViewData
                        {
                            Type       = gridBasedTypes[i],
                            Sprite     = _tileResourceSelector.GetGoalSprite(gridBasedTypes[i]),
                            TotalCount = _goalsSystem.GetLeftGoalCount(gridBasedTypes[i]),
                            LeftCount  = _goalsSystem.GetLeftGoalCount(gridBasedTypes[i])
                        }); 
                    }
                }
            }

            return result;
        }
    }
}