using System;
using UnityEngine;

namespace BBB.Match3
{
    [Serializable]
    public class PropellerComboFlightEffectSettings
    {
        public float FlightDurationMax = 2f;
        public float FlightDurationMin = 1f;
        public AnimationCurve Ease;
        public float PerpPercent = 0.2f;
        public AnimationCurve IntroCurveForLeftHolderX;
        public AnimationCurve IntroCurveForRightHolderX;
        public AnimationCurve IntroCurveForLeftHolderY;
        public AnimationCurve IntroCurveForRightHolderY;
        public float IntroTime = 0.5f;

        public AnimationCurve LoopCurveForLeftHolderX;
        public AnimationCurve LoopCurveForRightHolderX;
        public AnimationCurve LoopCurveForLeftHolderY;
        public AnimationCurve LoopCurveForRightHolderY;
        public float LoopTime = 0.5f;


        public float GetFlightDuration(float distanceInCells)
        {
            var normalizedDistance = distanceInCells / Grid.MaxDistance;
            return Mathf.Lerp(FlightDurationMin, FlightDurationMax, normalizedDistance);
        }
    }
}