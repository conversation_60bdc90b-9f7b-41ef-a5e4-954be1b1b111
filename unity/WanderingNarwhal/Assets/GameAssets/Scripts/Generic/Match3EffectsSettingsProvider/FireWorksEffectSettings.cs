using System;
using UnityEngine;

namespace BBB.Match3
{
    [Serializable]
    public class FireWorksEffectSettings
    {
        public int FireWorksNumberOfRockets = 3;
        public float FireWorksMinDuration = 2f;
        public float FireWorksMaxDuration = 2.4f;
        public bool FireWorksTargetCenterOfSquareMechanic = true;
        public float FireWorksSourcePositionOffset = 0.25f;
        public float FireWorksTargetPositionOffset = 0.25f;
        public float FireWorksDurationOffset = 0.05f;
        public float ReleaseFireWorksRocketTargetFx = 0.25f;
        public AnimationCurve FireWorksFlightScaleCurveX;
        public AnimationCurve FireWorksFlightScaleCurveY;
        public AnimationCurve FireWorksFlightScaleCurveZ;
        public float FireWorksDelayForLayerOne = 0.25f;
        public float FireWorksDelayForLayerTwo = 0.25f;
    }
}