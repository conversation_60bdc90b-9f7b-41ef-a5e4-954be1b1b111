using System;
using UnityEngine;

namespace BBB.Match3
{
    [Serializable]
    public struct MinMax
    {
        public float Min;
        public float Max;
    }
    
    [Serializable]
    public class PropellerEffectSettings
    {
        public MinMax FlightDuration = new() { Min = .97f, Max = 1.55f};
        public float PerpPercent = .7f;
        public AnimationCurve Ease;
        public float TargetScale = 1.3f;
        public AnimationCurve ScaleEase;
        public float ThirdPropellerDelay = 0.2f;
        public bool PropellerTargetCenterOfSquareMechanic = true;
        public Vector3 ShadowDistance;

        public float GetFlightDuration(float distanceInCells)
        {
            var normalizedDistance = distanceInCells / Grid.MaxDistance;
            return Mathf.Lerp(FlightDuration.Min, FlightDuration.Max, normalizedDistance);
        }
    }
}