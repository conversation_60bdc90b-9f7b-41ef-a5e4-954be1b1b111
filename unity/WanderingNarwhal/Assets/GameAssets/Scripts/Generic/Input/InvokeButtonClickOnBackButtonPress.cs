using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.Generic
{
    /// <summary>
    /// Component for invoking button action on system back button press (on Android) or Escape button press (on Windows).
    /// </summary>
    /// <remarks>
    /// Used to invoke close button click in dialogs.
    /// </remarks>
    [DisallowMultipleComponent]
    public class InvokeButtonClickOnBackButtonPress : BbbMonoBehaviour
    {
#if !UNITY_IOS || UNITY_EDITOR || BBB_DEBUG
        public enum InitializeOrderType
        {
            /// <summary>
            /// Initialize as soon as possible.
            /// </summary>
            Default,

            /// <summary>
            /// Initialize with delay (at the end of frame).
            /// </summary>
            Late,
        }

        [Tooltip(
            "Init order is useful when there are 2 buttons on same prefab, so button which initialized last will be prioritized when back button is pressed (unless it is disabled at same point in runtime).")]
        [SerializeField]
        private InitializeOrderType _initOrder = InitializeOrderType.Default;
        [SerializeField] private bool _ignoreClicks = false;

        private Button _button;

        private static List<InvokeButtonClickOnBackButtonPress> _instances = new(2);
        private static event Action OnInstancesCountChangedEvent;

        private bool _isCurrentlyMainListenner;

        private void Awake()
        {
            _button = GetComponent<Button>();
            if (_button == null && !_ignoreClicks)
                Debug.LogError($"Couldn't find button component", this.gameObject);
        }

        private void OnInstancesCountChanged()
        {
            _isCurrentlyMainListenner = _instances.Count > 0 && _instances[^1] == this;
        }

        private void Update()
        {
            if (!_isCurrentlyMainListenner)
            {
                return;
            }

            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (_button != null && _button.gameObject.activeInHierarchy && _button.interactable && !_ignoreClicks)
                {
                    _button.onClick.Invoke();
                }
            }
        }

        protected override void OnEnable()
        {
            if (_initOrder == InitializeOrderType.Default)
            {
                Enable();
            }
            else
            {
                StartCoroutine(LateEnable());
            }
        }

        private IEnumerator LateEnable()
        {
            yield return null;
            Enable();
        }

        private void Enable()
        {
            if (_instances.Contains(this))
                return;
            
            _instances.Add(this);
            OnInstancesCountChangedEvent?.Invoke();

            OnInstancesCountChangedEvent -= OnInstancesCountChanged;
            OnInstancesCountChangedEvent += OnInstancesCountChanged;
            _isCurrentlyMainListenner = true;
        }

        protected override void OnDisable()
        {
            OnInstancesCountChangedEvent -= OnInstancesCountChanged;
            _instances.Remove(this);
            OnInstancesCountChangedEvent?.Invoke();
        }

        public static bool TryDebugInvokeEscButtonForCurrentListeners()
        {
            if (_instances.Count == 0)
                return false;

            var lastItem = _instances[^1];
            if (!lastItem.gameObject.activeInHierarchy)
                return false;

            if (lastItem._ignoreClicks)
                return false;

            var btn = lastItem.GetComponent<Button>();
            if (btn == null)
                return false;
            btn.onClick.Invoke();

            return true;
        }
#endif
    }
}