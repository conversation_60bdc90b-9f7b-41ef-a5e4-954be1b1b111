using UnityEngine;

namespace BBB
{
    /// <summary>
    /// Component with single functionality: provide method for disable animator.
    /// </summary>
    public class DisableAnimatorOnTrigger : BbbMonoBehaviour
    {
        [SerializeField] private Animator _animator;

        /// <summary>
        /// Called from animation clip.
        /// </summary>
        private void DisableAnimator()
        {
            if (_animator == null)
                return;
            _animator.enabled = false;
        }
    }
}