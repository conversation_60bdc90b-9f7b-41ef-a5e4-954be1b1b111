using System;
using BBB.DI;
using BBB.Match3.Renderer;
using BBB.UI.Level.Scripts.Boosts;
using HedgehogTeam.EasyTouch;
using UnityEngine;
using BBB.Match3.Systems;
using Cysharp.Threading.Tasks;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;

namespace BBB.UI.Level.Input
{
    public class BoosterInputCancelled : IEvent
    {
    }

    public class BoostEnabledEvent : IEvent
    {
    }

    public class BoostDisabledEvent : IEvent
    {
    }

    public sealed class EasyTouchInputController : BbbMonoBehaviour, IInputController, IContextInitializable,
        IContextReleasable
    {
        public class CoordsPair
        {
            public readonly Coords FirstCoords;
            public readonly Coords SecondCoords;

            public CoordsPair(Coords firstCoords, Coords secondCoords)
            {
                FirstCoords = firstCoords;
                SecondCoords = secondCoords;
            }
        }

        public event Action<Coords> OnTileSelected = delegate { };
        public event Action<Coords, int> OnStartTouchEvent = delegate { };
        public event Action<Coords> OnTileTappedEvent = delegate { };
        public event Action<Coords> OnEndTouchEvent = delegate { };
        public event Action<Coords> OnTileLongHoldEvent = delegate { };
        public event Action BonusTimeSkipEvent = delegate { };

        private const float TapMaxDuration = 0.5f;
        private const float LongHoldTapDuration = 1f;
        private IGridController _gridController;
        private GameController _gameController;
        private IEventDispatcher _eventDispatcher;
        private IBoostButtonsController _boostButtonsController;
        private bool _initialized;
        private Match3SimulationPlayer _match3SimulationPlayer;
        private bool _allowSkip;
        private InputState _state;
        private Coords _lastTapCoords;

        public void InitializeByContext(IContext context)
        {
            _boostButtonsController = context.Resolve<IBoostButtonsController>();
            _gridController = context.Resolve<IGridController>();
            _gameController = context.Resolve<GameController>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _match3SimulationPlayer = context.Resolve<Match3SimulationPlayer>();
            _state = new InputState();

            _initialized = true;
            SubscribeInputEvents();
        }

        public void ReleaseByContext(IContext context)
        {
            UnsubscribeInputEvents();
        }

        protected override void OnEnable()
        {
            if (_initialized)
            {
                SubscribeInputEvents();
            }
        }

        protected override void OnDisable()
        {
            if (_initialized)
            {
                UnsubscribeInputEvents();
            }
        }

        private void SubscribeInputEvents()
        {
            UnsubscribeInputEvents();
            EasyTouch.On_TouchStart += OnStart;
            EasyTouch.On_TouchDown += OnDown;
            EasyTouch.On_TouchUp += OnUp;
            _boostButtonsController.OnBoostEnabled += OnBoostEnabled;
            _boostButtonsController.OnBoostDisabled += OnBoostDisabled;
        }

        private void UnsubscribeInputEvents()
        {
            EasyTouch.On_TouchStart -= OnStart;
            EasyTouch.On_TouchDown -= OnDown;
            EasyTouch.On_TouchUp -= OnUp;
            _boostButtonsController.OnBoostEnabled -= OnBoostEnabled;
            _boostButtonsController.OnBoostDisabled -= OnBoostDisabled;
        }

        private void ProcessSwap(Coords first, Coords second, bool blocked = false)
        {
            if (blocked)
                return;

            var input = new PlayerInputSwap(new CoordsPair(first, second));
            _gameController.PlayerInputAsync(input).Forget();
        }

        public void AutoDoubleTap(Coords coords)
        {
            if (_gameController.IsInputLocked)
                return;

            TapTile(coords, 2);
        }

        private void TapTile(Coords coords, int tapCount)
        {
            if (!IsTapAllowed(coords))
            {
                TriggerBonusTimeSkip();
                return;
            }

            OnTileTappedEvent(coords);

            ProcessTap(coords, tapCount, _gameController.IsInputLocked);
        }

        private void ProcessTap(Coords coords, int count, bool blocked = false)
        {
            if (blocked || coords.Equals(Coords.OutOfGrid))
                return;

            IPlayerInput input;
            if (count < 2)
            {
                input = new PlayerInputSingleTap(coords);
            }
            else
            {
                input = new PlayerInputDoubleTap(coords);
            }

            _gameController.PlayerInputAsync(input).Forget();
        }

        public void TriggerBonusTimeSkip()
        {
            if (_gameController.GameEnded && _gameController.HasWon && !_gameController.IsLevelEnded) // compete simulation to skip the bonus time
            {
                BonusTimeSkipEvent?.Invoke();
            }
        }

        public void AutoSwap(Coords firstCellCoords, Coords secondCellCoords)
        {
            if (_gameController.IsInputLocked)
                return;
            
            if (!_gridController.HasTile(firstCellCoords))
                return;

            ProcessSwap(firstCellCoords, secondCellCoords);
        }

        private void OnStart(Gesture gesture)
        {
            OnStartAsync(gesture).Forget();
        }

        private async UniTaskVoid OnStartAsync(Gesture gesture)
        {
            if (_gameController.IsInputLocked)
                return;

            _state.IsLongHold = false;

            if (_state.Boost == null)
            {
                OnStartNormal(gesture);
                OnStartTouchEvent(_state.FirstCoords, _state.TapCount);
            }
            else
            {
                StartTouch(Coords.OutOfGrid);
                OnStartTouchEvent(_state.FirstCoords, _state.TapCount);
                await OnStartBoosted(gesture);
            }
        }

        private void OnDown(Gesture gesture)
        {
            OnDownAsync(gesture).Forget();
        }

        private async UniTaskVoid OnDownAsync(Gesture gesture)
        {
            if (_gameController.IsInputLocked)
            {
                if (_allowSkip)
                {
                    TriggerBonusTimeSkip();
                    _allowSkip = false;
                }

                return;
            }

            if (_state.Boost == null)
            {
                OnDownNormal(gesture);
            }
            else
            {
                await OnDownBoosted(gesture);
            }
        }

        private void OnUp(Gesture gesture)
        {
            OnUpAsync(gesture).Forget();
        }

        private async UniTaskVoid OnUpAsync(Gesture gesture)
        {
            if (_gameController.IsInputLocked)
            {
                _allowSkip = true;
                return;
            }

            if (_state.Boost == null)
            {
                OnUpNormal(gesture);
                OnEndTouchEvent(_state.FirstCoords);
            }
            else
            {
                OnEndTouchEvent(_state.FirstCoords);
                await OnUpBoosted(gesture);
            }
        }

        private void OnStartNormal(Gesture gesture)
        {
            _state.SwipeBlocked = false;
            _state.IsSwipe = false;
            _state.StartTime = Time.time;
            var coords = _gridController.GetGridCoords(gesture.position);
            if (coords == _lastTapCoords && _state.StartTime - _state.LastUpTime <= TapMaxDuration)
            {
                _state.TapCount++;
            }
            else
            {
                _state.TapCount = 1;
            }

            _lastTapCoords = coords;

            if (_state.IsTap)
            {
                _state.IsTap = false;
                StartTouch(coords);
            }
            else
            {
                StartTouch(coords);
            }
        }

        private void OnDownNormal(Gesture gesture)
        {
            if (_state.SwipeBlocked)
                return;

            if (_state.IsSwipe || CheckForCoordChange(gesture))
            {
                ProcessSwipe(gesture);
                _state.IsSwipe = true;
            }
            else
            {
                var tapDuration = Time.time - _state.StartTime;
                if (tapDuration >= LongHoldTapDuration && !_state.IsLongHold)
                {
                    _state.IsLongHold = true;
                    OnTileLongHoldEvent(_state.FirstCoords);
                }
            }
        }

        private void OnUpNormal(Gesture gesture)
        {
            _state.LastUpTime = Time.time;
            if (_state.IsSwipe)
            {
                _state.IsSwipe = false;
            }
            else
            {
                _state.IsTap = !CheckForCoordChange(gesture);
            }

            if (_state.IsTap)
            {
                var tapDuration = _state.LastUpTime - _state.StartTime;
                if (tapDuration <= TapMaxDuration)
                {
                    TapTile(_state.FirstCoords, _state.TapCount);
                }
            }
        }

        private async UniTask OnStartBoosted(Gesture gesture)
        {
            if (_gameController.IsInputLocked)
                return;

            var inputCoords = _gridController.GetGridCoords(gesture.position);

            if (!_gameController.IsAllowedToUseBooster(inputCoords))
                return;

            if (inputCoords == Coords.OutOfGrid)
            {
                _eventDispatcher.TriggerEventNextFrame(_eventDispatcher.GetMessage<BoosterInputCancelled>());
                return;
            }

            var success = await _state.Boost.Handler.Start(inputCoords, _state);

            if (success)
            {
                FinishBoost();
            }
        }

        private async UniTask OnDownBoosted(Gesture gesture)
        {
            if (_gameController.IsInputLocked)
                return;

            var inputCoords = _gridController.GetGridCoords(gesture.position);
            if (inputCoords == Coords.OutOfGrid)
                return;

            if (!_gameController.IsAllowedToUseBooster(inputCoords))
                return;

            var success = await _state.Boost.Handler.Down(inputCoords, _state);

            if (success)
            {
                FinishBoost();
            }
        }

        private async UniTask OnUpBoosted(Gesture gesture)
        {
            if (_gameController.IsInputLocked)
                return;

            var inputCoords = _gridController.GetGridCoords(gesture.position);
            if (inputCoords == Coords.OutOfGrid)
                return;

            if (!_gameController.IsAllowedToUseBooster(inputCoords))
                return;

            var result = await _state.Boost.Handler.Up(inputCoords, _state);

            if (result)
            {
                FinishBoost();
            }
        }

        private void OnBoostEnabled(BoostWrapper boost)
        {
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<BoostEnabledEvent>());
            _state.ResetBoostRelated();
            _state.Boost = boost;
        }

        private void OnBoostDisabled(BoostWrapper boost)
        {
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<BoostDisabledEvent>());
            _state.Boost = null;
        }

        private void FinishBoost()
        {
            if (_state is { Boost: not null })
            {
                _state.Boost.Finish();
                _state.Boost = null;
            }
        }

        private void StartTouch(Coords coords)
        {
            _state.FirstCoords = coords;
        }

        private bool CheckForCoordChange(Gesture gesture)
        {
            return _state.FirstCoords != _gridController.GetGridCoords(gesture.position);
        }

        private bool IsSwipePossible(Gesture gesture, out Coords swapCandidateCoords)
        {
            swapCandidateCoords = default;

            if (!_gridController.HasTile(_state.FirstCoords))
                return false;

            var swipeFinishCoords = _gridController.GetGridCoords(gesture.position);
            if (swipeFinishCoords == Coords.OutOfGrid)
                swipeFinishCoords = _gridController.GetOutGridCoords(gesture.position);

            Vector2 dir = (swipeFinishCoords.ToUnityVector2() - _state.FirstCoords.ToUnityVector2()).normalized;
            if (Math.Abs(dir.x * dir.y) > 0.01f)
                return false;

            swapCandidateCoords = _state.FirstCoords + new Coords((int)dir.x, (int)dir.y);

            return true;
        }

        private bool IsTapAllowed(Coords tapCandidateCoords)
        {
            return _gameController.IsTileAllowedToTap(tapCandidateCoords)
                   && !_match3SimulationPlayer.IsPlayingSimulationFor(_gameController.OriginalGrid)
                   && !_gameController.GameEnded;
        }

        private void ProcessSwipe(Gesture gesture)
        {
            if (_gameController.GameEnded) return;

            if (!IsSwipePossible(gesture, out Coords swapCandidateCoords))
                return;

            if (!_gameController.AreTilesAllowedToSwap(_state.FirstCoords, swapCandidateCoords))
                return;

            _state.SecondCoords = swapCandidateCoords;
            _state.SwipeBlocked = true;
            ProcessSwap(_state.FirstCoords, _state.SecondCoords, _gameController.IsInputLocked);
        }
    }
}