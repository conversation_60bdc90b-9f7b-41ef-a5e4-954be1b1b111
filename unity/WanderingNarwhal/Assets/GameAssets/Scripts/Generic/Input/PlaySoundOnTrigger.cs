using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Generic
{
    public class PlaySoundOnTrigger : BbbMonoBehaviour
    {
        [SerializeField] private string _soundId;
        [SerializeField] private bool _enabled = true;

        [SerializeField] private GameObject _relevantObject;

        /// <summary>
        /// Should be called from animation clips.
        /// </summary>
        public void PlayTriggeredSound()
        {
            if ((_relevantObject != null && !_relevantObject.activeInHierarchy) || !_enabled || _soundId.IsNullOrEmpty()) return;

            AudioProxy.PlaySound(_soundId);
        }
    }
}