using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Generic
{
    [RequireComponent(typeof(ParticleSystem))]
    public class PlaySoundOnParticlesSpawn : BbbMonoBehaviour
    {
        [SerializeField]
        private float _minimalDelay = 0.5f;

        [SerializeField]
        private string _soundId;

        [SerializeField] private int _limitNumberOfSounds = 2;
        private int _playedNumerOfTimes;

        private ParticleSystem _ps;
        private int            _lastParticlesCount;
        private float          _lastPlayTime;

        private void Awake()
        {
            _ps = GetComponent<ParticleSystem>();
        }

        private void Update()
        {
            if (!_ps.emission.enabled) return;

            var count = _ps.particleCount;
            if (count > _lastParticlesCount)
            {
                var time = Time.time;
                if (time >= _lastPlayTime + _minimalDelay)
                {
                    _lastPlayTime = time;
                    PlaySound();
                }
            }

            _lastParticlesCount = count;
        }

        private void PlaySound()
        {
            if (_playedNumerOfTimes > _limitNumberOfSounds)
                return;
            
            _playedNumerOfTimes++;
            AudioProxy.PlaySound(_soundId);
        }

        protected override void OnDisable()
        {
            _playedNumerOfTimes = 0;
        }
    }
}