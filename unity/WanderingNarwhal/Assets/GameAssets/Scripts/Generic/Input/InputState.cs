using BBB.UI.Level.Scripts.Boosts;
using UnityEngine;

namespace BBB.UI.Level.Input
{
    public class InputState
    {
        public Coords FirstCoords;
        public Coords SecondCoords;
        public bool IsSwipe;
        public bool SwipeBlocked;
        public bool IsTap;
        public BoostWrapper Boost;
        public float StartTime;
        public float LastUpTime;
        public int TapCount = 1;
        public bool IsLongHold;


        public void ResetBoostRelated()
        {
            IsSwipe = false;
            IsTap = false;
            Boost = null;
            StartTime = 0f;
        }
    }

}

