using System.Collections.Generic;
using BBB;
using BBB.DI;
using BBB.Navigation;
using FBConfig;

namespace GameAssets.Scripts.Generic.Carrots
{
    public class CarrotsManager : ICarrotsManager
    {
        private const string CarrotsLockUid = "carrots";
        
        private ILevelsOrderingManager _levelsOrderingManager;
        private ILockManager _lockManager;
        private readonly List<CarrotConfig> _carrotConfigs = new();

        private bool CheckIfCarrotsIsLocked =>
            _lockManager != null && _lockManager.IsLocked(CarrotsLockUid, LockItemType.Other);

        public void Init(IContext context)
        {
            _levelsOrderingManager = context.Resolve<ILevelsOrderingManager>();
            _lockManager = context.Resolve<ILockManager>();
            var config = context.Resolve<IConfig>();
            if (config != null)
            {
                SetupCarrotsConfig(config);
            }
        }

        private void SetupCarrotsConfig(IConfig config)
        {
            var carrotsConfig = config.TryGetDefaultFromDictionary<CarrotsConfig>();
            
            if (carrotsConfig.IsNull() || carrotsConfig.CarrotsLength <= 0)
            {
                return;
            }

            for (var i = 0; i < carrotsConfig.CarrotsLength; i++)
            {
                var carrotConfig = carrotsConfig.Carrots(i);
                if (carrotConfig.HasValue)
                {
                    _carrotConfigs.Add(carrotConfig.Value);
                }
            }
        }

        public CarrotsData GetCarrotsDataForLevel(string levelUid)
        {
            if (string.IsNullOrEmpty(levelUid) || CheckIfCarrotsIsLocked)
            {
                return default;
            }

            var closestData = new CarrotsData();
            var minDifference = int.MaxValue;
            var foundValidCarrotItem = false;

            foreach (var carrot in _carrotConfigs)
            {
                var difference = _levelsOrderingManager.GetLevelsDifference(carrot.LevelUid, levelUid);
                var isValidCarrot = _levelsOrderingManager.IsLevelAFurtherThanLevelB(carrot.LevelUid, levelUid);

                if (isValidCarrot && difference > 0 && difference < minDifference)
                {
                    minDifference = difference;
                    closestData = new CarrotsData
                    {
                        LevelName = _levelsOrderingManager.GetLevelName(carrot.LevelUid),
                        NameTextUid = carrot.NameTextId,
                        PrefabUid = carrot.Prefab
                    };
                    foundValidCarrotItem = true;
                }
            }

            return foundValidCarrotItem ? closestData : default;
        }
    }
}