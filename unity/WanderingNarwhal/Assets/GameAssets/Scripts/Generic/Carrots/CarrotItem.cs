using BBB;
using BBB.Core;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.UI.Core;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Generic.Carrots
{
    public class CarrotItem : BbbMonoBehaviour
    {
        [SerializeField] private LocalizedTextPro _descriptionText;
        [SerializeField] private AsyncLoadableImage _carrotImage;

        public void SetCarrotsImage(string carrotsIconUid)
        {
            if (_carrotImage != null)
            {
                _carrotImage.Show(carrotsIconUid);
            }
        }
        
        public void SetDescriptionText(string description, params object[] args)
        {
            if (_descriptionText != null)
            {
                _descriptionText.SetTextId(description, args);
            }
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            if (_carrotImage != null)
            {
                _carrotImage.Hide();
            }

            if (_descriptionText != null)
            {
                _descriptionText.ClearText();
            }
        }
    }
}