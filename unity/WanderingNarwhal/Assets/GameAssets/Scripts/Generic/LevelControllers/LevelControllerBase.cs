using System;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.UI.Level;
using BBB.UI.Level.Input;
using BBB.UI.Level.Scripts.Boosts;
using BBB.Wallet;
using BebopBee;
using Bebopbee.Core.Extensions.Unity;
using BebopBee.Core.Audio;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Generic;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Messages;
using UnityEngine;
#if USE_NUNU_SDK && BBB_DEBUG
using GameAssets.Scripts.Nunu.NunuFlayerFunctions;
#endif

namespace BBB.UI
{
    public class LevelStarted : Message<string, int>
    {
    }

    /// <summary>
    /// Container for level instance reference.
    /// </summary>
    /// <remarks>
    /// Content of this container is changed on every transition to the Level (or `HH`) screen.
    /// </remarks>
    public class LevelHolder
    {
        public ILevel level;
    }

    public abstract class LevelControllerBase : BbbMonoBehaviour, IContextReleasable, IEditorModeDeterminer
    {
        public abstract bool IsEditor { get; }

        protected bool ShouldShowQuitWarning => _startingMoves > _currentMoves || GameController.BoostersUsed;

        protected GameController GameController;
        protected GoalResourcesPanel GoalPanel;
        protected GoalsSystem GoalsSystem => _goalsSystem;
        protected HintSystem HintSystemRef => _hintSystem;
        protected TilesResources TileResources;
        protected TurnsPanelController TurnsPanel;
        protected BackgroundImageController BackgroundImageController;
        protected SuperBoostPanelController SuperBoostPanel;
        protected ButlerGiftGlobeController ButlerGiftGlobe;
        protected ILivesManager _livesManager;
        protected IBoostButtonsController _boostButtons;
        protected LevelHolder _levelHolder;
        protected IPlayerManager _playerManager;
        protected IAccountManager _accountManager;
        protected ILocationManager _locationManager;


        private IMatch3SharedResourceProvider _match3SharedResourceProvider;
        private GridController _gridController;
        private Match3CharacterController _characterController;
        private AwardRenderer _awardsRenderer;
        private OverlaysRenderer _overlaysRenderer;
        private BoostDescriptionPanel _boostDescriptionPanel;
        private AssistValuesPanelController _assistValuesPanelController;
        private TileResourceSelector _tilesResourcesExtra;
        private TileMechanicHelpPanel _tileHelpPanel;
        private ILevel _initialLevelState;
        protected IEventDispatcher _eventDispatcher;

        protected IContext Context;

        private int _startingMoves;
        private int _currentMoves;
        private bool _levelInitialized;

        // Static variables are used because HelpingHands and Level view presenters are different instances, but they must re-use same services from base class. -VK

        #region static_services

        private static Match3LevelContext _screenContext;

        private static GoalsSystem _goalsSystem;
        private static M3SpawnSystem _spawnSystem;
        private static Match3SimulationPlayer _simulationPlayer;
        private static TileTickPlayer _tileTickPlayer;
        private static CellController _cellController;
        private static GoalViewHelper _goalViewHelper;
        private static HintSystem _hintSystem;
        private static HighlightSystem _highlightSystem;
        private static SuperBoostSystem _superBoostSystem;
        private static Match3SpecialVoiceoversPlayer _specialVoiceoversPlayer;
        private static AutoBoosterInputFactory _autoBoosterInputFactory;
        private static PerimeterRenderer _perimeterRenderer;
        private static HintPerimeterRenderer _hintPerimeterRenderer;
        private static ExtraBoostersHelper _extraBoostersHelper;

        protected static M3Settings M3Settings;
        protected static TileController TileController;
        protected static TileRevealer TileRevealer;
        protected static RendererContainers _rendererContainers;

        #endregion

        public virtual void Init(IContext previousContext)
        {
            ILevel level;
            if (_playerManager == null)
            {
                // This method can be called multiple times when we invoke transition to Level screen or HelpingHands screen if caching is enabled for them.
                // In that case we can skip resolving of most global services and components. -VK
                _playerManager = previousContext.Resolve<IPlayerManager>();
                _accountManager = previousContext.Resolve<IAccountManager>();
                _locationManager = previousContext.Resolve<ILocationManager>();
                _eventDispatcher = previousContext.Resolve<IEventDispatcher>();
                _livesManager = previousContext.Resolve<ILivesManager>();
                level = _playerManager.CurrentLevel;
                _levelHolder = previousContext.Resolve<LevelHolder>();
                _levelHolder.level = level;
                AggregateProxyDependencies();
                AggregateDependencies();
            }
            else
            {
                level = _playerManager.CurrentLevel;
                _levelHolder.level = level;
            }

            _startingMoves = level.TurnsLimit;
            _currentMoves = level.TurnsLimit;

            _initialLevelState = new GameAssets.Scripts.Player.Level();
            _initialLevelState.Grid = level.Grid.Clone();
            _initialLevelState.Goals = level.Goals.Clone();
            _initialLevelState.TurnsLimit = level.TurnsLimit;
            Context = InitializeContext(previousContext);
            _match3SharedResourceProvider = Context.Resolve<IMatch3SharedResourceProvider>();
            enabled = true;

            TileResources.RefreshForLevel(level);
            _tilesResourcesExtra.RefreshForLevel(level);
            _gridController.SetupLevel(level);
            _goalsSystem.RefreshForLevel(level);
            _hintSystem.SetupForLevel(level);
            _boostButtons.InitButtonsForLevel(level);
            Subscribe();

            OnContextInitialized(previousContext, Context);
            _levelInitialized = true;
        }

        public virtual void OnShow()
        {
            AudioProxy.AddContext(_match3SharedResourceProvider.GetAudioContext(Match3ResKeys.Match3SoundsContext));
            AudioProxy.AddContext(_match3SharedResourceProvider.GetAudioContext(Match3ResKeys.Match3VoiceoverContext));
            TileResources.CalculateCellSize(_gridController);
            _hintPerimeterRenderer.SetupSize();
            SetupRendererContainers(_levelHolder.level);
            _characterController.PrepareFennec();
            GoalPanel.FillGoalsAsync(_levelHolder.level).Forget();
            BackgroundImageController.Refresh();
            _boostDescriptionPanel.Preload(_levelHolder.level);
            SuperBoostPanel.Show();
            _tileTickPlayer.Launch();
            Subscribe();
        }
        
        protected virtual void SetupRendererContainers(ILevel level)
        {
            _rendererContainers.SetupContainers(level);
        }

        public virtual void OnHide()
        {
            //_boostDescriptionPanel.Release();
            _boostDescriptionPanel.Hide();
            _simulationPlayer?.Hide();
            _tileHelpPanel?.HidePanelImmediate();
            GoalPanel.Clear();
            var soundsContext = _match3SharedResourceProvider.GetAudioContext(Match3ResKeys.Match3SoundsContext);
            if (soundsContext != null)
                AudioProxy.RemoveContext(soundsContext);
            var voiceoverContext = _match3SharedResourceProvider.GetAudioContext(Match3ResKeys.Match3VoiceoverContext);
            if (voiceoverContext != null)
                AudioProxy.RemoveContext(voiceoverContext);
            _rendererContainers?.ForceReleaseAllSpawned();
            if (SuperBoostPanel != null)
                SuperBoostPanel.Hide();
            _tileTickPlayer?.Stop();
            Unsubscribe();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            ReleaseScreenContext();

            Context?.Releaser.ReleaseContext();
            Context = null;
        }

        protected virtual void ReleaseScreenContext()
        {
            _screenContext?.Releaser.ReleaseContext();
            _screenContext = null;
        }

        public virtual bool IsLevelInitialized()
        {
            return _levelInitialized;
        }

        protected virtual void RunGame(bool skipLevelStartNarrative = false)
        {
            int movesIncreasedByBooster = _playerManager.PlayerInventory.GetBoosterAmount(InventoryItems.ExtraMoveBooster);
            _startingMoves += movesIncreasedByBooster;
            _currentMoves += movesIncreasedByBooster;
        }

        protected virtual async UniTask RetryLevelAfterLose()
        {
            var level = _levelHolder.level;
            if (level == null)
            {
                BDebug.LogError(LogCat.Match3, "[RetryLevelAfterLose] Level has been cleared in the level holder");
                if (_playerManager != null) //restore level
                {
                    level = _playerManager.CurrentLevel;
                    _levelHolder.level = level;
                }
            }

            _startingMoves = _levelHolder.level.TurnsLimit;
            _currentMoves = _levelHolder.level.TurnsLimit;

            _gridController.Clear();
            await GoalPanel.FillGoalsAsync(_levelHolder.level);
            TurnsPanel.Setup(_levelHolder.level.TurnsLimit);

            //state
            _levelHolder.level.Goals = _initialLevelState.Goals.Clone();
            _levelHolder.level.Grid = _initialLevelState.Grid.Clone();
            _levelHolder.level.TurnsLimit = _initialLevelState.TurnsLimit;

            GameController.ResetOnRetry(_levelHolder.level);
            if (_goalsSystem == null)
            {
                _goalsSystem = new GoalsSystem();
                _goalsSystem.InitializeByContext(Context);
            }

            GoalsSystem.ResetOnRetry(_levelHolder.level);

            //view
            await GoalPanel.FillGoalsAsync(_levelHolder.level);
            TurnsPanel.Setup(_levelHolder.level.TurnsLimit);
            //ScoreProgressSliderController.ResetOnRestart(_levelHolder.level);
            _superBoostSystem ??= new SuperBoostSystem();
            _superBoostSystem.ResetOnRetry(_levelHolder.level);
            SuperBoostPanel.ResetOnRetry(_levelHolder.level);
        }

        protected virtual void OnContextInitialized(IContext previusContext, IContext context)
        {
        }

        private void Unsubscribe()
        {
            GameController.InputLocked -= InputLockedHandler;

            _eventDispatcher.RemoveListener<RemainingMovesChanged>(OnRemainingMovesChanged);
            _eventDispatcher.RemoveListener<LevelResultPredicted>(OnLevelResultPredicted);
            _eventDispatcher.RemoveListener<LevelEndedEvent>(OnLevelEnded);
        }

        private void Subscribe()
        {
            Unsubscribe();

            GameController.InputLocked += InputLockedHandler;

            _eventDispatcher.AddListener<RemainingMovesChanged>(OnRemainingMovesChanged);
            _eventDispatcher.AddListener<LevelResultPredicted>(OnLevelResultPredicted);
            _eventDispatcher.AddListener<LevelEndedEvent>(OnLevelEnded);
        }

        private void InputLockedHandler(bool inputLocked)
        {
            _boostButtons.LockInput(inputLocked);
        }

        public virtual void ReleaseByContext(IContext context)
        {
            Unsubscribe();

            _screenContext = null;
            _rendererContainers = null;
            _goalsSystem = null;
            _spawnSystem = null;
            _simulationPlayer = null;
            _tileTickPlayer = null;
            _cellController = null;
            TileController = null;
            _goalViewHelper = null;
            _hintSystem = null;
            _highlightSystem = null;
            _superBoostSystem = null;
            _specialVoiceoversPlayer = null;
            _autoBoosterInputFactory = null;
        }

        protected virtual void AggregateProxyDependencies()
        {
            var proxy = GetComponent<LevelControllerReferenceProxy>();
            TileResources = proxy.TileResources;
            _tilesResourcesExtra = proxy.TilesResourcesExtra;
            M3Settings = proxy.M3Settings;
        }

        private void AggregateDependencies()
        {
            if (GameController == null) GameController = GetComponentInChildren<GameController>();
            if (GoalPanel == null) GoalPanel = GetComponentInChildren<GoalResourcesPanel>();
            if (TurnsPanel == null) TurnsPanel = GetComponentInChildren<TurnsPanelController>();
            if (BackgroundImageController == null) BackgroundImageController = GetComponentInChildren<BackgroundImageController>();
            if (_characterController == null) _characterController = GetComponentInChildren<Match3CharacterController>();
            if (_awardsRenderer == null) _awardsRenderer = GetComponentInChildren<AwardRenderer>();
            if (_overlaysRenderer == null) _overlaysRenderer = GetComponentInChildren<OverlaysRenderer>();
            if (_boostDescriptionPanel == null) _boostDescriptionPanel = GetComponentInChildren<BoostDescriptionPanel>();
            if (SuperBoostPanel == null) SuperBoostPanel = GetComponentInChildren<SuperBoostPanelController>();
            if (_assistValuesPanelController == null) _assistValuesPanelController = GetComponentInChildren<AssistValuesPanelController>(true);
            if (_tileHelpPanel == null) _tileHelpPanel = GetComponentInChildren<TileMechanicHelpPanel>(includeInactive: true);
            if (_boostButtons == null) _boostButtons = GetComponentInChildren<IBoostButtonsController>();
            if (ButlerGiftGlobe == null) ButlerGiftGlobe = GetComponentInChildren<ButlerGiftGlobeController>();
        }

        private IContext InitializeContext(IContext previusContext)
        {
            _levelHolder = previusContext.Resolve<LevelHolder>();
            _gridController = GetComponentInChildren<GridController>();

            if (_screenContext == null)
            {
                // Initialize static sub-services.
                // This base level controller class is used for two kinds of scenes: regular m3 scene and HelpingHands m3 scene,
                // each of these scenes have it's own level controller instance.
                // Static variables are used to share some of services between instances of level controllers,
                // because these level controllers are never active at the same time
                // and they always present in memory after initial scene loading (by screens pooling system). -VK
                _rendererContainers = new RendererContainers();
                _goalsSystem = new GoalsSystem();
                _spawnSystem = new M3SpawnSystem();
                _simulationPlayer = new Match3SimulationPlayer();
                _tileTickPlayer = new TileTickPlayer();
                _cellController = new CellController();
                TileController = new TileController();
                TileRevealer = new TileRevealer();
                _goalViewHelper = new GoalViewHelper();
                _hintSystem = new HintSystem();
                _highlightSystem = new HighlightSystem();
                _superBoostSystem = new SuperBoostSystem();
                _specialVoiceoversPlayer = new Match3SpecialVoiceoversPlayer();
                _autoBoosterInputFactory = new AutoBoosterInputFactory();
                _perimeterRenderer = GetComponentInChildren<PerimeterRenderer>();
                _hintPerimeterRenderer = GetComponentInChildren<HintPerimeterRenderer>();
                _extraBoostersHelper = new ExtraBoostersHelper();

                _screenContext = new Match3LevelContext();
            }

            _screenContext.AddServiceToRegisterOverride<LevelControllerBase>(this);
            _screenContext.AddServiceToRegisterOverride<GameController>(GameController);
            _screenContext.AddServiceToRegisterOverride<M3SpawnSystem>(_spawnSystem);
            RegisterSpecificServices(_screenContext, previusContext);
            _screenContext.AddServiceToRegisterOverride<TileMechanicHelpPanel>(_tileHelpPanel);
            _screenContext.AddServiceToRegisterOverride<TilesResources>(TileResources);
            _screenContext.AddServiceToRegisterOverride<TileResourceSelector>(_tilesResourcesExtra);
            _screenContext.AddServiceToRegisterOverride<M3Settings>(M3Settings);
            _screenContext.AddServiceToRegisterOverride<IM3CharacterAnimator>(_characterController);
            _screenContext.AddServiceToRegisterOverride<IInputController>(gameObject.GetOrAddComponent<EasyTouchInputController>());
            _screenContext.AddServiceToRegisterOverride<RendererContainers>(_rendererContainers);
            _screenContext.AddServiceToRegisterOverride<IGridController>(_gridController);
            _screenContext.AddServiceToRegisterOverride<IGridoverlayContainer>(_gridController);
            var fxRenderer = _gridController.GetOrAddComponent<FxRenderer>();
            _screenContext.AddServiceToRegisterOverride<FxRenderer>(fxRenderer);
            _screenContext.AddServiceToRegisterOverride<IPreSwapInitialtedEffectRenderer>(fxRenderer);
            _screenContext.AddServiceToRegisterOverride<CollectedEventTilesRenderer>(_gridController.GetOrAddComponent<CollectedEventTilesRenderer>());
            _screenContext.AddServiceToRegisterOverride<ICellController>(_cellController);
            _screenContext.AddServiceToRegisterOverride<TileController>(TileController);
            _screenContext.AddServiceToRegisterOverride<TileRevealer>(TileRevealer);
            _screenContext.AddServiceToRegisterOverride<GoalsSystem>(_goalsSystem);
            _screenContext.AddServiceToRegisterOverride<GoalViewHelper>(_goalViewHelper);
            _screenContext.AddServiceToRegisterOverride<Match3SimulationPlayer>(_simulationPlayer);
            _screenContext.AddServiceToRegisterOverride<TileTickPlayer>(_tileTickPlayer);
            _screenContext.AddServiceToRegisterOverride<OverlaysRenderer>(_overlaysRenderer);
            _screenContext.AddServiceToRegisterOverride<TurnsPanelController>(TurnsPanel);
            _screenContext.AddServiceToRegisterOverride<GoalResourcesPanel>(GoalPanel);
            _screenContext.AddServiceToRegisterOverride<IPerimeterRenderer>(_perimeterRenderer);
            _screenContext.AddServiceToRegisterOverride<IHintPerimeterRenderer>(_hintPerimeterRenderer);
            _screenContext.AddServiceToRegisterOverride<HintSystem>(_hintSystem);
            _screenContext.AddServiceToRegisterOverride<HighlightSystem>(_highlightSystem);
            _screenContext.AddServiceToRegisterOverride<SuperBoostSystem>(_superBoostSystem);
            _screenContext.AddServiceToRegisterOverride<AwardRenderer>(_awardsRenderer);
            _screenContext.AddServiceToRegisterOverride<Match3SpecialVoiceoversPlayer>(_specialVoiceoversPlayer);
            _screenContext.AddServiceToRegisterOverride<IBoostDescriptionPanel>(_boostDescriptionPanel);
            _screenContext.AddServiceToRegisterOverride<IBoostButtonsController>(_boostButtons);
            _screenContext.AddServiceToRegisterOverride<SuperBoostPanelController>(SuperBoostPanel);
            _screenContext.AddServiceToRegisterOverride<IAutoBoostInputFactory>(_autoBoosterInputFactory);
            UpdateBackgroundImageGetter(previusContext);
            _screenContext.AddServiceToRegisterOverride<BackgroundImageController>(BackgroundImageController);
            _screenContext.AddServiceToRegisterOverride<AssistValuesPanelController>(_assistValuesPanelController);
            _screenContext.AddServiceToRegisterOverride<ExtraBoostersHelper>(_extraBoostersHelper);
            _screenContext.AddServiceToRegisterOverride<ButlerGiftGlobeController>(ButlerGiftGlobe);
            OnInitiliazeContext(_screenContext, previusContext);
            _screenContext.RegisterContextOverride(previusContext);
#if USE_NUNU_SDK && BBB_DEBUG
            NunuFlayerFunctionsManager.InitializeLevelFunctions(_screenContext);
#endif
            return _screenContext;
        }

        private void UpdateBackgroundImageGetter(IContext previusContext)
        {
            var backgroundGetterType = GetBackgroundGetterType(previusContext);
            if (BackgroundImageController.BackgroundImageGetter == null || backgroundGetterType != BackgroundImageController.BackgroundImageGetter.GetType())
                BackgroundImageController.BackgroundImageGetter = Activator.CreateInstance(backgroundGetterType) as IBackgroundImageGetter;
        }

        protected virtual Type GetBackgroundGetterType(IContext previusContext)
        {
            return typeof(LocationBasedBackgroundImageGetter);
        }

#if BBB_DEBUG || UNITY_EDITOR
        private const float SlowDownTimeScale = 0.1f;
        private const float SpeedUpTimeScale = 10f;

        private enum TimeScaleMode
        {
            None = 0,
            Slow = 1,
            Fast = 2
        }

        private static TimeScaleMode _timeScaleMode = TimeScaleMode.None;
        private static float _cachedTimeScale;
        private void Update()
        {
            var shiftKeyPressed = Input.GetKey(KeyCode.LeftShift);
            var sKeyPressed = Input.GetKey(KeyCode.S);

            switch (_timeScaleMode)
            {
                case TimeScaleMode.None:
                    if (shiftKeyPressed && sKeyPressed)
                    {
                        _cachedTimeScale = Time.timeScale;
                        Time.timeScale = SpeedUpTimeScale;
                        _timeScaleMode = TimeScaleMode.Fast;
                    }

                    if (shiftKeyPressed && !sKeyPressed)
                    {
                        _cachedTimeScale = Time.timeScale;
                        Time.timeScale = SlowDownTimeScale;
                        _timeScaleMode = TimeScaleMode.Slow;
                    }

                    break;
                case TimeScaleMode.Slow:
                    if (shiftKeyPressed && sKeyPressed)
                    {
                        Time.timeScale = SpeedUpTimeScale;
                        _timeScaleMode = TimeScaleMode.Fast;
                    }

                    if (!shiftKeyPressed && !sKeyPressed)
                    {
                        Time.timeScale = _cachedTimeScale;
                        _timeScaleMode = TimeScaleMode.None;
                    }

                    break;
                case TimeScaleMode.Fast:
                    if (shiftKeyPressed && !sKeyPressed)
                    {
                        Time.timeScale = SlowDownTimeScale;
                        _timeScaleMode = TimeScaleMode.Slow;
                    }

                    if (!shiftKeyPressed && !sKeyPressed)
                    {
                        Time.timeScale = _cachedTimeScale;
                        _timeScaleMode = TimeScaleMode.None;
                    }

                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

#if UNITY_EDITOR
            if (Input.GetKeyDown(KeyCode.Tab) && IsEditor)
            {
                // Timescale toggle 1->2->4->0.5->1
                Time.timeScale *= 2f;
                if (Time.timeScale > 15f)
                {
                    Time.timeScale = 0.125f;
                }

                Debug.Log("#TIMESCALE# Set Time scale = " + Time.timeScale.ToString("0.0"));
            }
#endif
        }
#endif

        public virtual void SetSuperDiscoBallValue(float updatedValue)
        {
            M3Settings.SuperDiscoBall = updatedValue;
        }

        protected virtual void OnInitiliazeContext(UnityContext currentContext, IContext previousContext)
        {
        }

        protected abstract void RegisterSpecificServices(UnityContext unityContext, IContext previousContext);

        protected abstract void OnLevelResultPredicted(LevelResultPredicted ev);

        protected abstract void OnLevelEnded(LevelEndedEvent ev);

        protected virtual void OnRemainingMovesChanged(RemainingMovesChanged ev)
        {
            var moves = ev.Arg0;

            _currentMoves = moves;

            if (TurnsPanel != null)
                TurnsPanel.Setup(moves);
        }

        public void DisposeContext()
        {
            OnDestroy();
        }
    }
}