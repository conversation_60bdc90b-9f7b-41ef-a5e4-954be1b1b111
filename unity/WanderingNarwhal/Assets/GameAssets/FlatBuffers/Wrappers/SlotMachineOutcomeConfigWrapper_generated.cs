using System;
using System.Collections;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using FBConfig;
using UnityEngine.Profiling;

namespace Core.Configs
{
    internal class SlotMachineOutcomeConfigWrapper : IDictionary<string, SlotMachineOutcomeConfig>, IFlatbufferWrapper
    {
        private readonly FlatbufferConfig _collectionProvider;

        private SlotMachineOutcomeConfigDict? _collection;
        private SlotMachineOutcomeConfigDict? Collection
        {
            get
            {
                if (!_collection.HasValue)
                {
                    _collection = _collectionProvider.SlotMachineOutcomeConfig;
                }
                return _collection;
            }
        }

        private int CollectionLength => Collection?.ValuesLength ?? 0;
        private Dictionary<string, SlotMachineOutcomeConfig> _internalDict;
        private Dictionary<string, SlotMachineOutcomeConfig> InternalDict => _internalDict ??= new();
        private Dictionary<int, string> _indexKeyMap;
        private Dictionary<int, string> IndexKeyMap => _indexKeyMap ??= new();
        public string Hash => Collection?.Hash ?? string.Empty;

        public SlotMachineOutcomeConfigWrapper(SlotMachineOutcomeConfigDict? collection)
        {
            _collection = collection;
        }

        public SlotMachineOutcomeConfigWrapper(FlatbufferConfig collectionProvider)
        {
            _collectionProvider = collectionProvider;
        }

        public IEnumerator<KeyValuePair<string, SlotMachineOutcomeConfig>> GetEnumerator()
        {
            if (!Collection.HasValue)
                yield break;

            for (int i = 0; i < CollectionLength; i++)
            {
                var item = this[i];
                yield return new KeyValuePair<string, SlotMachineOutcomeConfig>(item.Uid, item);
            }
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public void Add(KeyValuePair<string, SlotMachineOutcomeConfig> item)
        {
            throw new NotImplementedException();
        }

        public void Clear()
        {
            throw new NotImplementedException();
        }

        public bool Contains(KeyValuePair<string, SlotMachineOutcomeConfig> item)
        {
            return TryGetValue(item.Key, out var itemValue) && itemValue.Equals(item.Value);
        }

        public void CopyTo(KeyValuePair<string, SlotMachineOutcomeConfig>[] array, int arrayIndex)
        {
            throw new NotImplementedException();
        }

        public bool Remove(KeyValuePair<string, SlotMachineOutcomeConfig> item)
        {
            throw new NotImplementedException();
        }

        public int Count => CollectionLength;

        public bool IsReadOnly => true;

        public void Add(string key, SlotMachineOutcomeConfig value)
        {
            throw new NotImplementedException();
        }

        public bool ContainsKey(string key)
        {
            return TryGetValue(key, out _);
        }

        private bool TryLookupByKey(string key, out SlotMachineOutcomeConfig value)
        {
            if (InternalDict.TryGetValue(key, out value))
                return true;

            Profiler.BeginSample("SlotMachineOutcomeConfigWrapper.TryLookupByKey");
            value = default;
            if (!Collection.HasValue)
            {
                Profiler.EndSample();
                return false;
            }

            var min = 0;
            var max = CollectionLength - 1;
            while (min <= max)
            {
                var mid = (min + max) / 2;
                value = this[mid];
                InternalDict[value.Uid] = value;
                var comp = string.CompareOrdinal(value.Uid, key);
                if (comp == 0)
                {
                    Profiler.EndSample();
                    return true;
                }

                if (comp > 0)
                {
                    max = mid - 1;
                }
                else
                {
                    min = mid + 1;
                }
            }
            Profiler.EndSample();
            value = default;
            return false;
        }

        public bool Remove(string key)
        {
            throw new NotImplementedException();
        }

        public bool TryGetValue(string key, out SlotMachineOutcomeConfig value)
        {
            Profiler.BeginSample("SlotMachineOutcomeConfigWrapper.TryGetValue");
            var result = TryLookupByKey(key, out value);
            Profiler.EndSample();
            return result;
        }

        public SlotMachineOutcomeConfig this[string key]
        {
            get
            {
                TryGetValue(key, out var item);
                return item;
            }
            set => InternalDict[key] = value;
        }

        private SlotMachineOutcomeConfig this[int index]
        {
            get
            {
                if (IndexKeyMap.TryGetValue(index, out var key) && InternalDict.TryGetValue(key, out var item))
                {
                    return item;
                }

                var newValue = Collection?.Values(index);
                if (newValue.HasValue)
                {
                    var uid = newValue.Value.Uid;
                    IndexKeyMap[index] = uid;
                    if (InternalDict.TryGetValue(uid, out var existingItem))
                        return existingItem;

                    InternalDict[uid] = newValue.Value;
                }
                return newValue ?? default;
            }
        }

        public ICollection<string> Keys => throw new NotImplementedException();
        public ICollection<SlotMachineOutcomeConfig> Values
        {
            get
            {
#if UNITY_EDITOR
                // for editor usage only (like tests and level editor), SlotMachineOutcomeConfigWrapper shouldn't be used to iterate over all values
                BDebug.LogError(LogCat.Config, "SlotMachineOutcomeConfigWrapper.Values is implemented for editor usage only. This will throw an exception on device. Use FlatBufferConfigResolver instead.");
                var result = new SlotMachineOutcomeConfig[CollectionLength];
                for (int i = 0; i < CollectionLength; i++)
                    result[i] = this[i];
                return result;
#else
                throw new NotImplementedException();
#endif
            }
        }
    }
}
